#!/usr/bin/env python3
"""
数据库创建脚本 - 用于manage_duckdb.sh调用
"""

import sys
import os

# 添加backend路径到sys.path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
backend_path = os.path.join(project_root, 'backend')
sys.path.insert(0, backend_path)

# 路径配置完成

def main():
    if len(sys.argv) != 2:
        print("用法: python create_database.py <数据库路径>")
        sys.exit(1)
    
    db_path = sys.argv[1]
    
    # 如果路径不是绝对路径，转换为绝对路径
    if not os.path.isabs(db_path):
        db_path = os.path.abspath(db_path)
    
    # 确保数据库目录存在
    db_dir = os.path.dirname(db_path)
    if not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
        print(f"创建数据库目录: {db_dir}")
    
    try:
        from database.duckdb_manager import DuckDBManager
        from database.init_new_storage import initialize_new_storage
        from database.init_user_behavior_storage import UserBehaviorStorageInitializer
        
        print(f"正在创建数据库: {db_path}")
        
        # 创建数据库连接
        db_manager = DuckDBManager(db_path)
        print('数据库连接建立成功')
        
        # 1. 初始化基础表结构
        db_manager.initialize_database()
        print('基础表结构初始化完成')
        
        # 2. 初始化认证表结构
        print('正在初始化认证表结构...')
        auth_schema_file = os.path.join(backend_path, 'database', 'schema', 'auth_tables.sql')
        if os.path.exists(auth_schema_file):
            with open(auth_schema_file, 'r', encoding='utf-8') as f:
                auth_schema_sql = f.read()
            db_manager.execute_script(auth_schema_sql)
            print('认证表结构初始化完成')
        else:
            print('警告: 认证表SQL文件不存在，跳过认证表初始化')
        
        # 3. 初始化新的算法存储结构
        success = initialize_new_storage()
        if success:
            print('新算法存储结构初始化完成')
        else:
            print('新算法存储结构初始化失败')
            sys.exit(1)
        
        # 4. 初始化用户行为分析表结构
        print('正在初始化用户行为分析表结构...')
        behavior_initializer = UserBehaviorStorageInitializer()
        if behavior_initializer.initialize_storage():
            print('用户行为分析表结构初始化完成')
        else:
            print('警告: 用户行为分析表结构初始化失败')
        
        print('\n🎉 数据库初始化完成')
        print('✅ 已创建的表结构包括:')
        print('   - 基础业务表 (users, user_relationships, tasks, etc.)')
        print('   - 认证管理表 (auth_users, auth_user_sessions, etc.)')
        print('   - 算法结果表 (algorithm_results, wash_trading_results, etc.)')
        print('   - 用户行为分析表 (user_trading_profiles, validation_results, etc.)')
        print('\n💡 提示: 使用 ./manage_duckdb.sh restore-admin 创建默认管理员用户')
        
        # 在初始化完成后，确保新字段存在
        print('正在更新表结构...')
        try:
            db_manager = DuckDBManager(db_path)
            
            # 确保trade_sequence字段存在
            try:
                db_manager.execute_sql("ALTER TABLE position_analysis ADD COLUMN trade_sequence TEXT DEFAULT NULL")
            except:
                pass  # 字段已存在
            
            try:
                db_manager.execute_sql("ALTER TABLE incomplete_positions_waiting ADD COLUMN trade_sequence TEXT DEFAULT NULL")
            except:
                pass  # 字段已存在
            
            print('✅ 表结构更新完成')
        except Exception as e:
            print(f'⚠️  表结构更新警告: {e}')
        
    except ImportError as e:
        print(f'模块导入失败: {e}')
        print(f'当前Python路径: {sys.path}')
        print(f'Backend路径: {backend_path}')
        print(f'Backend路径存在: {os.path.exists(backend_path)}')
        sys.exit(1)
    except Exception as e:
        print(f'数据库初始化失败: {e}')
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
