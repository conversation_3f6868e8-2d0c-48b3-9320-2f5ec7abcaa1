"""
任务健康监控和恢复服务
用于检测和修复任务状态不一致的问题
"""

import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from database.repositories.task_repository import task_repository
from database.duckdb_manager import db_manager

logger = logging.getLogger(__name__)

class TaskHealthMonitor:
    """任务健康监控器"""
    
    def __init__(self):
        self.recovery_enabled = True
    
    def check_orphaned_tasks(self) -> List[Dict]:
        """检查孤儿任务（有算法结果但没有任务记录的）"""
        try:
            # 查找algorithm_results中存在但tasks表中不存在的任务ID
            sql = """
            SELECT DISTINCT ar.task_id, ar.created_at, ar.algorithm_type, COUNT(*) as result_count
            FROM algorithm_results ar
            LEFT JOIN tasks t ON ar.task_id = t.task_id
            WHERE t.task_id IS NULL
            GROUP BY ar.task_id, ar.created_at, ar.algorithm_type
            ORDER BY ar.created_at DESC
            """
            
            orphaned_tasks = db_manager.execute_sql(sql)
            
            if orphaned_tasks:
                logger.warning(f"发现 {len(orphaned_tasks)} 个孤儿任务")
                for task in orphaned_tasks:
                    logger.warning(f"  - 孤儿任务: {task['task_id']} ({task['algorithm_type']}, {task['result_count']} 条结果)")
            
            return orphaned_tasks
            
        except Exception as e:
            logger.error(f"检查孤儿任务失败: {e}")
            return []
    
    def check_stuck_tasks(self, timeout_hours: int = 2) -> List[Dict]:
        """检查卡住的任务（长时间处于pending或processing状态）"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=timeout_hours)

            sql = """
            SELECT task_id, task_type, status, filename, created_at, updated_at
            FROM tasks
            WHERE status IN ('pending', 'processing')
            AND created_at < ?
            ORDER BY created_at ASC
            """

            stuck_tasks = db_manager.execute_sql(sql, [cutoff_time])

            if stuck_tasks:
                logger.warning(f"发现 {len(stuck_tasks)} 个卡住的任务")
                for task in stuck_tasks:
                    logger.warning(f"  - 卡住任务: {task['task_id']} ({task['status']}, 创建于 {task['created_at']})")

            return stuck_tasks

        except Exception as e:
            logger.error(f"检查卡住任务失败: {e}")
            return []
    
    def recover_orphaned_task(self, task_id: str) -> bool:
        """恢复孤儿任务"""
        try:
            logger.info(f"开始恢复孤儿任务: {task_id}")
            
            # 从algorithm_results获取任务信息
            sql = """
            SELECT task_id, created_at, algorithm_type, COUNT(*) as result_count
            FROM algorithm_results 
            WHERE task_id = ?
            GROUP BY task_id, created_at, algorithm_type
            """
            
            results = db_manager.execute_sql(sql, [task_id])
            if not results:
                logger.error(f"无法找到任务 {task_id} 的算法结果")
                return False
            
            result = results[0]
            
            # 生成文件名
            created_at = result['created_at']
            if isinstance(created_at, str):
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    filename = f"recovered_{result['algorithm_type']}_{dt.strftime('%Y%m%d_%H%M%S')}.csv"
                except:
                    filename = f"recovered_{result['algorithm_type']}.csv"
            else:
                filename = f"recovered_{result['algorithm_type']}_{created_at.strftime('%Y%m%d_%H%M%S')}.csv"
            
            # 重建任务记录
            task_repository.create_task(task_id, 'contract_analysis', filename)
            
            # 更新为完成状态
            message = f"恢复的任务：{result['algorithm_type']}，共 {result['result_count']} 条结果"
            task_repository.update_task_status(task_id, 'completed', 100, message)
            
            logger.info(f"✅ 孤儿任务恢复成功: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 恢复孤儿任务失败: {task_id}, 错误: {e}")
            return False
    
    def fix_stuck_task(self, task_id: str) -> bool:
        """修复卡住的任务"""
        try:
            logger.info(f"开始修复卡住任务: {task_id}")
            
            # 检查是否有算法结果
            sql = "SELECT COUNT(*) as count FROM algorithm_results WHERE task_id = ?"
            result_count = db_manager.execute_sql(sql, [task_id])
            
            if result_count and result_count[0]['count'] > 0:
                # 有结果，标记为完成
                count = result_count[0]['count']
                message = f"修复的任务：发现 {count} 条算法结果"
                task_repository.update_task_status(task_id, 'completed', 100, message)
                logger.info(f"✅ 卡住任务修复为完成状态: {task_id}")
            else:
                # 无结果，标记为失败
                message = "任务超时，无算法结果"
                task_repository.update_task_status(task_id, 'failed', 0, message)
                logger.info(f"✅ 卡住任务修复为失败状态: {task_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 修复卡住任务失败: {task_id}, 错误: {e}")
            return False
    
    def run_health_check(self, auto_fix: bool = True) -> Dict:
        """运行完整的健康检查"""
        logger.info("开始任务健康检查...")
        
        results = {
            'orphaned_tasks': [],
            'stuck_tasks': [],
            'recovered_count': 0,
            'fixed_count': 0,
            'errors': []
        }
        
        try:
            # 检查孤儿任务
            orphaned_tasks = self.check_orphaned_tasks()
            results['orphaned_tasks'] = orphaned_tasks
            
            # 检查卡住的任务
            stuck_tasks = self.check_stuck_tasks()
            results['stuck_tasks'] = stuck_tasks
            
            if auto_fix and self.recovery_enabled:
                # 自动恢复孤儿任务
                for task in orphaned_tasks:
                    if self.recover_orphaned_task(task['task_id']):
                        results['recovered_count'] += 1
                    else:
                        results['errors'].append(f"恢复孤儿任务失败: {task['task_id']}")
                
                # 自动修复卡住的任务
                for task in stuck_tasks:
                    if self.fix_stuck_task(task['task_id']):
                        results['fixed_count'] += 1
                    else:
                        results['errors'].append(f"修复卡住任务失败: {task['task_id']}")
            
            logger.info(f"健康检查完成: 恢复 {results['recovered_count']} 个孤儿任务, "
                       f"修复 {results['fixed_count']} 个卡住任务")
            
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            results['errors'].append(f"健康检查异常: {str(e)}")
        
        return results

# 创建全局监控器实例
task_health_monitor = TaskHealthMonitor()
