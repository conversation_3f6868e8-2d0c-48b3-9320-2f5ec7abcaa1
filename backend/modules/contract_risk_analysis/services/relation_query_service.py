"""
合约关联查询服务
负责查询指定用户及其下属关联账户的异常金额数据
"""

import logging
import tempfile
import os
from datetime import datetime
from typing import List, Dict, Any, Set
from database.duckdb_manager import db_manager

logger = logging.getLogger(__name__)

class ContractRelationQueryService:
    """合约关联查询服务类"""
    
    def __init__(self):
        self.logger = logger
        self.db_manager = db_manager
    
    def create_relation_query_task(self, user_id: str, include_direct_only: bool = True, task_id: str = None) -> Dict[str, Any]:
        """创建关联查询任务"""
        try:
            # 验证输入
            if not user_id:
                raise ValueError("user_id不能为空")

            self.logger.info(f"开始关联查询用户 {user_id} 及其下属账户的合约异常金额")

            # 1. 获取用户的下属关联账户
            related_users = self._get_related_users(user_id, include_direct_only)
            
            if not related_users:
                raise ValueError(f"未找到用户 {user_id} 的关联账户数据")

            # 包含原始用户ID
            all_user_ids = [user_id] + related_users
            self.logger.info(f"关联查询范围：目标用户1个，关联用户{len(related_users)}个，总计{len(all_user_ids)}个")

            # 2. 转换ID格式（如果需要）
            converted_ids = self._convert_ids_to_member_ids(all_user_ids)
            
            if not converted_ids:
                raise ValueError("没有找到有效的用户数据")

            self.logger.info(f"ID转换完成：输入{len(all_user_ids)}个ID，转换成功{len(converted_ids)}个member_id")

            # 3. 查询各类算法的异常金额
            results = self._query_abnormal_amounts(list(converted_ids.values()), task_id)

            # 4. 生成Excel文件（包含关联关系信息）
            file_path = self._generate_relation_excel_file(user_id, all_user_ids, results, converted_ids, related_users, include_direct_only)

            return {
                'status': 'success',
                'download_url': f'/api/contract/batch-query-download/{file_path.split("/")[-1]}',
                'file_path': file_path,
                'target_user': user_id,
                'related_users_count': len(related_users),
                'total_users': len(all_user_ids),
                'converted_users': len(converted_ids),
                'results_count': len(results),
                'query_mode': 'direct_only' if include_direct_only else 'full_tree'
            }

        except Exception as e:
            self.logger.error(f"关联查询失败: {str(e)}")
            raise

    def _get_related_users(self, user_id: str, include_direct_only: bool) -> List[str]:
        """获取用户的下属关联账户（基于BD团队）"""
        try:
            # 使用基于users表的BD团队查询（与BD金字塔保持一致）
            related_users = self._get_related_users_from_bd_team(user_id, include_direct_only)

            if related_users:
                self.logger.info(f"通过BD团队查询找到 {len(related_users)} 个关联用户")
                return related_users

            self.logger.warning(f"未找到用户 {user_id} 的任何关联账户")
            return []

        except Exception as e:
            self.logger.error(f"获取关联用户失败: {str(e)}")
            return []

    def _get_related_users_from_bd_team(self, user_id: str, include_direct_only: bool) -> List[str]:
        """基于users表的BD团队获取关联用户（新的主要方法）"""
        try:
            # 1. 获取目标用户的BD信息
            user_bd_sql = """
            SELECT bd_name, analyzed_level_number, analyzed_level_type
            FROM users
            WHERE member_id = ? OR digital_id = ?
            LIMIT 1
            """

            user_bd_results = self.db_manager.execute_sql(user_bd_sql, [user_id, user_id])

            if not user_bd_results:
                self.logger.warning(f"未找到用户 {user_id} 的BD信息")
                return []

            user_bd_info = user_bd_results[0]
            bd_name = user_bd_info['bd_name']
            user_level = user_bd_info['analyzed_level_number']

            if not bd_name:
                self.logger.warning(f"用户 {user_id} 没有BD归属")
                return []

            self.logger.info(f"用户 {user_id} 属于BD: {bd_name}, 层级: L{user_level}")

            # 2. 根据include_direct_only参数决定查询范围
            if include_direct_only:
                # 只查询直接下级（下一层级）
                target_level = user_level + 1
                bd_users_sql = """
                SELECT member_id
                FROM users
                WHERE bd_name = ? AND analyzed_level_number = ?
                ORDER BY member_id
                """
                params = [bd_name, target_level]

                self.logger.info(f"查询BD {bd_name} 中L{target_level}层级的直接下级用户")

            else:
                # 查询所有下级（所有更高层级的用户）
                bd_users_sql = """
                SELECT member_id
                FROM users
                WHERE bd_name = ? AND analyzed_level_number > ?
                ORDER BY analyzed_level_number, member_id
                """
                params = [bd_name, user_level]

                self.logger.info(f"查询BD {bd_name} 中L{user_level}以下所有层级的用户")

            # 3. 执行查询
            bd_users_results = self.db_manager.execute_sql(bd_users_sql, params)

            related_users = [user['member_id'] for user in bd_users_results]

            self.logger.info(f"在BD {bd_name} 中找到 {len(related_users)} 个关联用户")

            return related_users

        except Exception as e:
            self.logger.error(f"基于BD团队获取关联用户失败: {str(e)}")
            return []





    def _convert_ids_to_member_ids(self, user_ids: List[str]) -> Dict[str, str]:
        """转换用户ID到member_id的映射"""
        # 复用批量查询服务的ID转换逻辑
        from .batch_query_service import contract_batch_query_service
        return contract_batch_query_service._convert_ids_to_member_ids(user_ids)

    def _query_abnormal_amounts(self, member_ids: List[str], task_id: str = None) -> List[Dict]:
        """查询异常金额数据（支持任务ID过滤）"""
        try:
            # 添加调试日志
            self.logger.info(f"查询异常金额，member_ids数量: {len(member_ids)}, task_id: {task_id}")
            self.logger.info(f"前5个member_ids: {member_ids[:5]}")

            # 构建查询SQL - 支持任务ID过滤
            placeholders = ','.join(['?' for _ in member_ids])

            if task_id:
                # 使用任务ID过滤
                sql = f"""
                SELECT
                    crd.member_id,
                    crd.detection_type,
                    SUM(crd.abnormal_volume) as total_abnormal_volume,
                    COUNT(*) as risk_count,
                    AVG(crd.risk_score) as avg_risk_score,
                    MAX(crd.created_at) as latest_detection
                FROM contract_risk_details crd
                JOIN algorithm_results ar ON crd.algorithm_result_id = ar.id
                WHERE ar.task_id = ? AND crd.member_id IN ({placeholders})
                GROUP BY crd.member_id, crd.detection_type
                ORDER BY crd.member_id, crd.detection_type
                """
                params = [task_id] + member_ids
            else:
                # 不使用任务ID过滤（查询所有任务）
                sql = f"""
                SELECT
                    member_id,
                    detection_type,
                    SUM(abnormal_volume) as total_abnormal_volume,
                    COUNT(*) as risk_count,
                    AVG(risk_score) as avg_risk_score,
                    MAX(created_at) as latest_detection
                FROM contract_risk_details
                WHERE member_id IN ({placeholders})
                GROUP BY member_id, detection_type
                ORDER BY member_id, detection_type
                """
                params = member_ids

            results = self.db_manager.execute_sql(sql, params)

            self.logger.info(f"查询到 {len(results)} 条异常金额记录")

            # 添加结果调试日志
            if results:
                self.logger.info(f"查询结果示例: {results[0]}")
                # 按detection_type分组统计
                by_type = {}
                total_risk_count = 0
                for result in results:
                    detection_type = result.get('detection_type', 'unknown')
                    risk_count = result.get('risk_count', 0)
                    by_type[detection_type] = by_type.get(detection_type, 0) + risk_count
                    total_risk_count += risk_count
                self.logger.info(f"按类型统计: {by_type}")
                self.logger.info(f"原始记录总数: {total_risk_count}")

            return results

        except Exception as e:
            self.logger.error(f"查询异常金额失败: {str(e)}")
            return []

    def _generate_relation_excel_file(self, target_user: str, all_user_ids: List[str], 
                                    results: List[Dict], converted_ids: Dict[str, str], 
                                    related_users: List[str], include_direct_only: bool) -> str:
        """生成关联查询Excel文件"""
        try:
            import pandas as pd
            from openpyxl import Workbook
            from openpyxl.styles import PatternFill, Font, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows
            
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"contract_relation_query_{target_user}_{timestamp}.xlsx"
            file_path = os.path.join(temp_dir, filename)
            
            # 创建工作簿
            wb = Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            # 1. 创建关联关系概览表
            self._create_relation_overview_sheet(wb, target_user, related_users, include_direct_only)
            
            # 2. 创建异常金额汇总表
            self._create_abnormal_summary_sheet(wb, all_user_ids, results, converted_ids)
            
            # 3. 创建详细数据表
            self._create_detailed_data_sheet(wb, results)
            
            # 保存文件
            wb.save(file_path)
            
            self.logger.info(f"关联查询Excel文件生成成功: {file_path}")
            return file_path
            
        except Exception as e:
            self.logger.error(f"生成关联查询Excel文件失败: {str(e)}")
            raise

    def _create_relation_overview_sheet(self, wb, target_user: str, related_users: List[str], include_direct_only: bool):
        """创建关联关系概览表"""
        from openpyxl.styles import Font

        ws = wb.create_sheet("关联关系概览")

        # 设置标题
        ws['A1'] = "合约异常金额关联查询报告"
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:D1')

        # 查询信息
        ws['A3'] = "查询信息"
        ws['A3'].font = Font(bold=True)
        
        ws['A4'] = "目标用户ID:"
        ws['B4'] = target_user
        ws['A5'] = "查询模式:"
        ws['B5'] = "仅直接下属" if include_direct_only else "完整下属树"
        ws['A6'] = "关联用户数量:"
        ws['B6'] = len(related_users)
        ws['A7'] = "查询时间:"
        ws['B7'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 关联用户列表
        if related_users:
            ws['A9'] = "关联用户列表"
            ws['A9'].font = Font(bold=True)
            
            for i, user_id in enumerate(related_users, start=10):
                ws[f'A{i}'] = f"关联用户 {i-9}:"
                ws[f'B{i}'] = user_id

    def _create_abnormal_summary_sheet(self, wb, all_user_ids: List[str], results: List[Dict], converted_ids: Dict[str, str]):
        """创建异常金额汇总表"""
        from openpyxl.styles import PatternFill, Font

        # 复用批量查询服务的Excel生成逻辑，但添加关联关系标识
        from .batch_query_service import contract_batch_query_service

        # 这里可以调用批量查询服务的Excel生成方法，然后添加关联关系信息
        # 为了简化，我们创建一个基本的汇总表
        ws = wb.create_sheet("异常金额汇总")

        # 设置表头
        headers = ["用户ID", "用户类型", "对敲异常金额", "高频异常金额", "套利异常金额", "总异常金额"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # 填充数据
        target_user = all_user_ids[0] if all_user_ids else ""
        
        for row, user_id in enumerate(all_user_ids, 2):
            member_id = converted_ids.get(user_id, user_id)
            user_results = [r for r in results if str(r.get('member_id', '')) == str(member_id)]

            # 添加调试日志
            if user_results:
                self.logger.info(f"用户 {user_id} (member_id: {member_id}) 找到 {len(user_results)} 条异常记录")

            # 按detection_type分组计算各类异常金额
            wash_trading_amount = 0  # 对敲异常金额
            high_freq_amount = 0     # 高频异常金额
            arbitrage_amount = 0     # 套利异常金额

            for result in user_results:
                detection_type = result.get('detection_type', '')
                volume = float(result.get('total_abnormal_volume', 0))

                if detection_type in ['wash_trading', 'suspected_wash_trading']:
                    wash_trading_amount += volume
                elif detection_type in ['high_frequency_trading', 'high_frequency']:
                    high_freq_amount += volume
                elif detection_type in ['funding_rate_arbitrage', 'arbitrage']:
                    arbitrage_amount += volume

            total_amount = wash_trading_amount + high_freq_amount + arbitrage_amount

            # 确定用户类型
            user_type = "目标用户" if user_id == target_user else "关联用户"

            ws.cell(row=row, column=1, value=user_id)
            ws.cell(row=row, column=2, value=user_type)
            ws.cell(row=row, column=3, value=wash_trading_amount)
            ws.cell(row=row, column=4, value=high_freq_amount)
            ws.cell(row=row, column=5, value=arbitrage_amount)
            ws.cell(row=row, column=6, value=total_amount)
            
            # 目标用户高亮显示
            if user_id == target_user:
                for col in range(1, 7):  # 调整列数范围
                    ws.cell(row=row, column=col).fill = PatternFill(start_color="FFFF99", end_color="FFFF99", fill_type="solid")

    def _create_detailed_data_sheet(self, wb, results: List[Dict]):
        """创建详细数据表"""
        from openpyxl.styles import PatternFill, Font

        # 复用批量查询服务的详细数据表创建逻辑
        from .batch_query_service import contract_batch_query_service

        ws = wb.create_sheet("详细数据")

        if not results:
            ws['A1'] = "无异常数据"
            return

        # 设置表头
        headers = ["用户ID", "算法类型", "异常金额", "检测时间", "风险等级"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # 填充数据
        for row, result in enumerate(results, 2):
            ws.cell(row=row, column=1, value=result.get('member_id', ''))
            ws.cell(row=row, column=2, value=result.get('detection_type', ''))
            ws.cell(row=row, column=3, value=float(result.get('total_abnormal_volume', 0)))
            ws.cell(row=row, column=4, value=str(result.get('latest_detection', '')))
            ws.cell(row=row, column=5, value=result.get('avg_risk_score', 0))


# 创建全局实例
contract_relation_query_service = ContractRelationQueryService()
