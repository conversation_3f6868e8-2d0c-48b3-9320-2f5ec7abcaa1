"""
统一的用户分析数据访问层
合并user_api.py和user_behavior_api.py中重复的数据库查询逻辑
"""

import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
import json

from database.duckdb_manager import DuckDBManager
from database.repositories.user_repository import user_repository
from database.repositories.task_repository import task_repository
# 已废弃: from database.repositories.contract_risk_repository import contract_risk_repository
# 🔄 迁移到新存储: 使用 ContractDataAdapter 替代

logger = logging.getLogger(__name__)

class UnifiedUserDataRepository:
    """统一的用户数据访问仓库"""
    
    def __init__(self):
        self.db_manager = DuckDBManager()
    
    def get_unified_task_lists(self) -> Dict:
        """统一获取任务列表"""
        try:
            # 获取已完成的合约和代理关系分析任务
            contract_tasks = self._get_completed_tasks('contract_analysis')
            agent_tasks = self._get_completed_tasks('agent_analysis')
            
            return {
                'contract_tasks': contract_tasks,
                'agent_tasks': agent_tasks,
                'status': 'success'
            }
        except Exception as e:
            logger.error(f"获取统一任务列表失败: {str(e)}")
            raise
    
    def _get_completed_tasks(self, task_type: str) -> List[Dict]:
        """获取已完成的任务列表"""
        try:
            sql = """
            SELECT task_id, filename, created_at, updated_at, 
                   message, status
            FROM tasks 
            WHERE task_type = ? AND status = 'completed'
            ORDER BY created_at DESC
            """
            
            results = self.db_manager.execute_query(sql, [task_type])
            
            tasks = []
            for row in results:
                task = {
                    'id': row[0],  # 前端期望的字段名
                    'name': row[1] if row[1] else f"{task_type}_{row[0][:8]}",  # 前端期望的字段名
                    'task_id': row[0],  # 保留原字段名以兼容
                    'task_name': row[1] if row[1] else f"{task_type}_{row[0][:8]}",  # 保留原字段名以兼容
                    'created_at': row[2],
                    'updated_at': row[3],
                    'description': row[4] if row[4] else '',  # 使用message作为描述
                    'file_path': row[1] if row[1] else '',    # filename作为文件路径
                    'status': row[5]
                }
                tasks.append(task)
            
            logger.info(f"获取到 {len(tasks)} 个 {task_type} 任务")
            return tasks
            
        except Exception as e:
            logger.error(f"获取已完成任务失败 - task_type: {task_type}, 错误: {str(e)}")
            return []
    
    def get_latest_task_id(self, task_type: str) -> Optional[str]:
        """获取指定类型的最新任务ID"""
        try:
            sql = """
            SELECT task_id FROM tasks 
            WHERE task_type = ? AND status = 'completed'
            ORDER BY created_at DESC 
            LIMIT 1
            """
            
            results = self.db_manager.execute_query(sql, [task_type])
            
            if results:
                return results[0][0]
            return None
            
        except Exception as e:
            logger.error(f"获取最新任务ID失败 - task_type: {task_type}, 错误: {str(e)}")
            return None
    
    def get_unified_user_basic_info(self, user_id: str) -> Dict:
        """统一获取用户基础信息"""
        try:
            # 基础用户信息结构
            user_info = {
                'member_id': user_id,
                'digital_id': '',
                'user_type': '普通用户',
                'bd_name': '',
                'last_activity': '',
                'account_status': 'active',
                'registration_date': '',
                'kyc_status': 'unknown'
            }
            
            # 尝试从数据库获取更多信息
            try:
                # 查询用户基本信息（从users表获取digital_id，从position_analysis表获取最新活动时间）
                sql = """
                SELECT u.member_id, u.digital_id, u.created_at, 
                       COALESCE(MAX(p.created_at), u.created_at) as last_activity
                FROM users u 
                LEFT JOIN position_analysis p ON u.member_id = p.member_id
                WHERE u.member_id = ?
                GROUP BY u.member_id, u.digital_id, u.created_at
                LIMIT 1
                """
                
                results = self.db_manager.execute_query(sql, [user_id])
                if results:
                    row = results[0]
                    user_info['digital_id'] = row[1] if row[1] else ''
                    user_info['last_activity'] = row[3] if row[3] else ''
                
            except Exception as e:
                logger.warning(f"获取用户基础信息时出错: {str(e)}")
            
            return user_info
            
        except Exception as e:
            logger.error(f"获取用户基础信息失败 - user_id: {user_id}, 错误: {str(e)}")
            raise
    
    def get_comprehensive_user_associations(self, user_id: str, digital_id: str = '', agent_task_id: str = '') -> Dict:
        """获取用户关联信息（合并两个API的关联查询逻辑）"""
        try:
            associations = {
                'same_ip_users': [],
                'same_device_users': [],
                'related_accounts': [],
                'association_summary': {
                    'total_associated_users': 0,
                    'high_risk_associations': 0,
                    'association_types': []
                }
            }

            # 查询同IP用户
            same_ip_users = self._get_same_ip_users(user_id, agent_task_id)
            associations['same_ip_users'] = same_ip_users

            # 查询同设备用户
            same_device_users = self._get_same_device_users(user_id, agent_task_id)
            associations['same_device_users'] = same_device_users

            # 查询相关账户
            if digital_id:
                related_accounts = self._get_related_accounts_by_digital_id(digital_id, agent_task_id)
                associations['related_accounts'] = related_accounts
            
            # 计算同时共享IP和设备的用户
            both_shared_users = self._get_both_shared_users(same_ip_users, same_device_users)
            associations['both_shared_users'] = both_shared_users

            # 计算关联摘要
            all_associated = set()
            all_associated.update([u['member_id'] for u in same_ip_users])
            all_associated.update([u['member_id'] for u in same_device_users])
            all_associated.update([u['member_id'] for u in associations['related_accounts']])

            # 移除自己
            all_associated.discard(user_id)

            # 添加前端期望的统计字段
            associations['same_ip_count'] = len(same_ip_users)
            associations['same_device_count'] = len(same_device_users)
            associations['both_shared_count'] = len(both_shared_users)

            associations['association_summary'] = {
                'total_associated_users': len(all_associated),
                'high_risk_associations': len([u for u in same_ip_users if u.get('risk_level', 'high') == 'high']),
                'association_types': [
                    'same_ip' if same_ip_users else None,
                    'same_device' if same_device_users else None,
                    'related_accounts' if associations['related_accounts'] else None
                ]
            }
            associations['association_summary']['association_types'] = [
                t for t in associations['association_summary']['association_types'] if t
            ]

            return associations
            
        except Exception as e:
            logger.error(f"获取用户关联信息失败 - user_id: {user_id}, 错误: {str(e)}")
            return {
                'same_ip_users': [],
                'same_device_users': [],
                'related_accounts': [],
                'association_summary': {
                    'total_associated_users': 0,
                    'high_risk_associations': 0,
                    'association_types': []
                }
            }
    
    def _get_same_ip_users(self, user_id: str, agent_task_id: str = '') -> List[Dict]:
        """获取同IP用户"""
        try:
            # 首先将member_id转换为digital_id（因为shared_relationships表使用digital_id）
            digital_id = self._get_digital_id_from_member_id(user_id)
            if not digital_id:
                logger.warning(f"无法找到用户 {user_id} 对应的digital_id")
                return []

            # 查询shared_relationships表中的IP共享关系
            # 查找该用户作为user_a或user_b的所有IP共享记录
            sql = """
            SELECT DISTINCT
                CASE
                    WHEN user_a_mid = ? THEN user_b_mid
                    ELSE user_a_mid
                END as related_digital_id,
                shared_value as shared_ip,
                CASE
                    WHEN user_a_mid = ? THEN user_b_bd
                    ELSE user_a_bd
                END as bd_name,
                CASE
                    WHEN user_a_mid = ? THEN user_b_level
                    ELSE user_a_level
                END as agent_level,
                created_at
            FROM shared_relationships
            WHERE relationship_type = 'ip'
                AND (user_a_mid = ? OR user_b_mid = ?)
            ORDER BY created_at DESC
            LIMIT 100
            """

            params = [digital_id] * 3 + [digital_id, digital_id]
            results = self.db_manager.execute_query(sql, params)

            same_ip_users = []
            for row in results:
                related_digital_id = row[0]
                shared_ip = row[1]
                bd_name = row[2] or ''
                agent_level = row[3] or ''
                created_at = row[4]

                # 将related_digital_id转换为member_id
                related_member_id = self._get_member_id_from_digital_id(related_digital_id)

                user_data = {
                    'member_id': related_member_id or related_digital_id,  # 如果转换失败，使用digital_id
                    'digital_id': related_digital_id,
                    'user_name': '',  # shared_relationships表中没有用户名
                    'shared_ip': shared_ip,
                    'bd_name': bd_name,
                    'agent_level': agent_level,
                    'last_activity': created_at.strftime('%Y-%m-%d %H:%M:%S') if created_at else '',
                    'risk_score': 0,  # 暂时设为0，后续可以从其他表获取
                }
                same_ip_users.append(user_data)

            logger.info(f"找到用户 {user_id} (digital_id: {digital_id}) 的 {len(same_ip_users)} 个同IP用户")
            return same_ip_users

        except Exception as e:
            logger.warning(f"获取同IP用户失败: {str(e)}")
            return []
    
    def _get_same_device_users(self, user_id: str, agent_task_id: str = '') -> List[Dict]:
        """获取同设备用户"""
        try:
            # 首先将member_id转换为digital_id（因为shared_relationships表使用digital_id）
            digital_id = self._get_digital_id_from_member_id(user_id)
            if not digital_id:
                logger.warning(f"无法找到用户 {user_id} 对应的digital_id")
                return []

            # 查询shared_relationships表中的设备共享关系
            # 查找该用户作为user_a或user_b的所有设备共享记录
            sql = """
            SELECT DISTINCT
                CASE
                    WHEN user_a_mid = ? THEN user_b_mid
                    ELSE user_a_mid
                END as related_digital_id,
                shared_value as shared_device,
                CASE
                    WHEN user_a_mid = ? THEN user_b_bd
                    ELSE user_a_bd
                END as bd_name,
                CASE
                    WHEN user_a_mid = ? THEN user_b_level
                    ELSE user_a_level
                END as agent_level,
                created_at
            FROM shared_relationships
            WHERE relationship_type = 'device'
                AND (user_a_mid = ? OR user_b_mid = ?)
            ORDER BY created_at DESC
            LIMIT 100
            """

            params = [digital_id] * 3 + [digital_id, digital_id]
            results = self.db_manager.execute_query(sql, params)

            same_device_users = []
            for row in results:
                related_digital_id = row[0]
                shared_device = row[1]
                bd_name = row[2] or ''
                agent_level = row[3] or ''
                created_at = row[4]

                # 将related_digital_id转换为member_id
                related_member_id = self._get_member_id_from_digital_id(related_digital_id)

                user_data = {
                    'member_id': related_member_id or related_digital_id,  # 如果转换失败，使用digital_id
                    'digital_id': related_digital_id,
                    'user_name': '',  # shared_relationships表中没有用户名
                    'shared_device': shared_device,
                    'bd_name': bd_name,
                    'agent_level': agent_level,
                    'last_activity': created_at.strftime('%Y-%m-%d %H:%M:%S') if created_at else '',
                    'risk_score': 0,  # 暂时设为0，后续可以从其他表获取
                }
                same_device_users.append(user_data)

            logger.info(f"找到用户 {user_id} (digital_id: {digital_id}) 的 {len(same_device_users)} 个同设备用户")
            return same_device_users

        except Exception as e:
            logger.warning(f"获取同设备用户失败: {str(e)}")
            return []
    
    def _get_related_accounts_by_digital_id(self, digital_id: str, agent_task_id: str = '') -> List[Dict]:
        """根据digital_id获取相关账户"""
        try:
            if not digital_id:
                return []

            # 查询user_relationships表中的代理关系
            # 查找该digital_id作为parent或child的所有关系记录
            sql = """
            SELECT DISTINCT
                CASE
                    WHEN parent_digital_id = ? THEN child_digital_id
                    ELSE parent_digital_id
                END as related_digital_id,
                relationship_type,
                level_diff,
                created_at
            FROM user_relationships
            WHERE (parent_digital_id = ? OR child_digital_id = ?)
                AND (parent_digital_id != child_digital_id)
            ORDER BY created_at DESC
            LIMIT 50
            """

            params = [digital_id, digital_id, digital_id]
            results = self.db_manager.execute_query(sql, params)

            related_accounts = []
            for row in results:
                # 通过related_digital_id查找对应的member_id
                member_id = self.find_member_id_by_digital_id(row[0], agent_task_id)

                if member_id:
                    account_data = {
                        'member_id': member_id,
                        'digital_id': row[0],
                        'relationship_type': row[1] or 'unknown',
                        'level_diff': row[2] or 0,
                        'created_at': row[3].strftime('%Y-%m-%d %H:%M:%S') if row[3] else '',
                        'risk_score': 0,  # 暂时设为0
                        'user_name': ''   # 暂时为空，后续可以从users表获取
                    }
                    related_accounts.append(account_data)

            logger.info(f"找到digital_id {digital_id} 的 {len(related_accounts)} 个相关账户")
            return related_accounts

        except Exception as e:
            logger.warning(f"获取相关账户失败: {str(e)}")
            return []

    def _get_both_shared_users(self, same_ip_users: List[Dict], same_device_users: List[Dict]) -> List[Dict]:
        """获取同时共享IP和设备的用户"""
        try:
            if not same_ip_users or not same_device_users:
                return []

            # 创建IP用户的member_id集合
            ip_user_ids = {user['member_id'] for user in same_ip_users if user.get('member_id')}

            # 找出同时在设备用户列表中的用户
            both_shared_users = []
            for device_user in same_device_users:
                if device_user.get('member_id') in ip_user_ids:
                    # 合并IP和设备信息
                    ip_user = next((u for u in same_ip_users if u['member_id'] == device_user['member_id']), {})

                    combined_user = {
                        'member_id': device_user['member_id'],
                        'user_name': device_user.get('user_name', ''),
                        'shared_ip': ip_user.get('shared_ip', ''),
                        'shared_device': device_user.get('shared_device', ''),
                        'bd_name': device_user.get('bd_name', ''),
                        'agent_level': device_user.get('agent_level', ''),
                        'last_activity': device_user.get('last_activity', ''),
                        'risk_score': device_user.get('risk_score', 0),
                        'digital_id': device_user.get('digital_id', '')
                    }
                    both_shared_users.append(combined_user)

            logger.info(f"找到 {len(both_shared_users)} 个同时共享IP和设备的用户")
            return both_shared_users

        except Exception as e:
            logger.warning(f"获取同时共享用户失败: {str(e)}")
            return []

    def get_unified_transaction_details(self, user_id: str, task_id: str = '') -> Dict:
        """统一获取用户交易详情（合并两个API的交易查询逻辑）"""
        try:
            # 🔧 修复：使用正确的表结构查询contract_risk_details
            # 该表没有task_id字段，需要通过algorithm_results表进行关联
            
            # 如果没有指定task_id，获取最新的
            if not task_id:
                task_id = self.get_latest_task_id('contract_analysis')
                if not task_id:
                    logger.warning(f"未找到可用的合约分析任务")
                    return self._get_default_transaction_details()
            
            # 🔧 修复：正确的查询语句 - 通过algorithm_results表关联
            # 如果指定了task_id，先尝试查询指定任务的数据
            if task_id:
                sql = """
                SELECT crd.member_id, crd.contract_name, crd.detection_type,
                       crd.abnormal_volume, crd.risk_score, crd.trade_count,
                       crd.time_range, crd.counterparty_ids, crd.additional_data,
                       crd.created_at, ar.task_id
                FROM contract_risk_details crd
                JOIN algorithm_results ar ON crd.algorithm_result_id = ar.id
                WHERE crd.member_id = ? AND ar.task_id = ?
                ORDER BY crd.created_at DESC
                """
                results = self.db_manager.execute_query(sql, [user_id, task_id])

                # 如果指定任务ID没有数据，回退到查询所有任务
                if not results:
                    logger.warning(f"用户 {user_id} 在任务 {task_id} 中没有数据，尝试查询所有任务")
                    sql = """
                    SELECT crd.member_id, crd.contract_name, crd.detection_type,
                           crd.abnormal_volume, crd.risk_score, crd.trade_count,
                           crd.time_range, crd.counterparty_ids, crd.additional_data,
                           crd.created_at, ar.task_id
                    FROM contract_risk_details crd
                    JOIN algorithm_results ar ON crd.algorithm_result_id = ar.id
                    WHERE crd.member_id = ?
                    ORDER BY crd.created_at DESC
                    """
                    results = self.db_manager.execute_query(sql, [user_id])
                    logger.info(f"回退查询找到 {len(results)} 条记录")
            else:
                # 如果没有指定task_id，查询所有任务的数据
                sql = """
                SELECT crd.member_id, crd.contract_name, crd.detection_type,
                       crd.abnormal_volume, crd.risk_score, crd.trade_count,
                       crd.time_range, crd.counterparty_ids, crd.additional_data,
                       crd.created_at, ar.task_id
                FROM contract_risk_details crd
                JOIN algorithm_results ar ON crd.algorithm_result_id = ar.id
                WHERE crd.member_id = ?
                ORDER BY crd.created_at DESC
                """
                results = self.db_manager.execute_query(sql, [user_id])
            
            if not results:
                logger.info(f"用户 {user_id} 在任务 {task_id} 中没有交易记录")
                raise ValueError(f"用户 {user_id} 数据不足：未找到交易记录，无法进行分析")
            
            # 🔧 修复：处理交易数据 - 适配新的表结构
            transactions = []
            total_volume = 0
            risk_transactions = 0
            
            for row in results:
                # 解析additional_data中的详细信息（如果有的话）
                additional_data = {}
                try:
                    if row[8]:  # additional_data字段
                        additional_data = json.loads(row[8]) if isinstance(row[8], str) else row[8]
                except:
                    additional_data = {}
                
                # 解析counterparty_ids
                counterparty_list = []
                try:
                    if row[7]:  # counterparty_ids字段
                        counterparty_list = json.loads(row[7]) if isinstance(row[7], str) else row[7]
                        if not isinstance(counterparty_list, list):
                            counterparty_list = [counterparty_list]
                except:
                    counterparty_list = []
                
                transaction = {
                    'member_id': row[0],
                    'symbol': row[1] or 'UNKNOWN',  # contract_name
                    'side': additional_data.get('side', 'UNKNOWN'),
                    'quantity': additional_data.get('quantity', 0),
                    'price': additional_data.get('price', 0),
                    'timestamp': row[9],  # created_at
                    'counterparty': counterparty_list[0] if counterparty_list else '',
                    'counterparty_id': counterparty_list[0] if counterparty_list else '',  # 🔧 添加counterparty_id字段
                    'risk_score': float(row[4]) if row[4] else 0,  # risk_score
                    'additional_data': additional_data,  # 🔧 修复：直接使用解析后的数据
                    'trade_type': row[2] or 'unknown',  # detection_type
                    'contract_name': row[1] or 'UNKNOWN',
                    'detection_type': row[2] or 'unknown',
                    'abnormal_volume': float(row[3]) if row[3] else 0,
                    'trade_count': int(row[5]) if row[5] else 0,
                    'time_range': row[6] or '',
                    'counterparty_ids': counterparty_list,
                    'frequency': int(row[5]) if row[5] else 0,  # 🔧 添加frequency字段
                    'currency': row[1] or 'UNKNOWN',  # 🔧 添加currency字段
                    'trading_pair': row[1] or 'UNKNOWN'  # 🔧 添加trading_pair字段
                }
                
                # 计算交易金额 - 使用abnormal_volume作为主要指标
                volume = float(row[3]) if row[3] else 0  # abnormal_volume
                if volume == 0:
                    # 尝试从additional_data中获取
                    volume = additional_data.get('volume', 0)
                    if volume == 0 and transaction['quantity'] and transaction['price']:
                        volume = transaction['quantity'] * transaction['price']
                
                transaction['volume'] = volume
                total_volume += volume
                
                # 🔧 修复：对敲交易即使风险评分为0也应该被认为是风险交易
                if transaction['risk_score'] > 0.5 or transaction.get('detection_type') == 'wash_trading':
                    risk_transactions += 1
                
                transactions.append(transaction)
            
            # 🔧 修复：构建risk_transactions列表和risk_by_type统计
            # 对敲交易即使风险评分为0也应该被认为是风险交易
            logger.info(f"🔍 开始构建风险交易列表，原始交易数量: {len(transactions)}")

            risk_transaction_list = []
            for i, t in enumerate(transactions):
                risk_score = t.get('risk_score', 0)
                detection_type = t.get('detection_type', '')
                is_risk = risk_score > 0.5 or detection_type == 'wash_trading'

                if i < 3:  # 只记录前3条用于调试
                    logger.info(f"  交易{i+1}: detection_type='{detection_type}', risk_score={risk_score}, 是否风险交易={is_risk}")

                if is_risk:
                    risk_transaction_list.append(t)

            logger.info(f"🔍 筛选后风险交易数量: {len(risk_transaction_list)}")
            risk_by_type = {}

            for transaction in risk_transaction_list:
                detection_type = transaction.get('detection_type', 'unknown')
                if detection_type not in risk_by_type:
                    risk_by_type[detection_type] = {
                        'count': 0,
                        'total_volume': 0,
                        'avg_risk_score': 0,
                        'transactions': []
                    }

                risk_by_type[detection_type]['count'] += 1
                risk_by_type[detection_type]['total_volume'] += transaction.get('volume', 0)
                risk_by_type[detection_type]['transactions'].append(transaction)

            # 计算平均风险评分
            for risk_type in risk_by_type:
                type_data = risk_by_type[risk_type]
                if type_data['count'] > 0:
                    total_score = sum(t.get('risk_score', 0) for t in type_data['transactions'])
                    type_data['avg_risk_score'] = total_score / type_data['count']

            return {
                'transactions': transactions,
                'risk_transactions': risk_transaction_list,  # 🔧 添加风险交易列表
                'risk_by_type': risk_by_type,  # 🔧 添加按类型分组的风险交易
                'total_risk_transactions': len(risk_transaction_list),  # 🔧 添加风险交易总数
                'summary': {
                    'total_transactions': len(transactions),
                    'total_volume': total_volume,
                    'risk_transactions': risk_transactions,
                    'risk_ratio': risk_transactions / len(transactions) if transactions else 0
                },
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"获取用户交易详情失败 - user_id: {user_id}, task_id: {task_id}, 错误: {str(e)}")
            return self._get_default_transaction_details()
    
    def _get_default_transaction_details(self) -> Dict:
        """获取默认的交易详情结构"""
        return {
            'transactions': [],
            'summary': {
                'total_transactions': 0,
                'total_volume': 0,
                'risk_transactions': 0,
                'risk_ratio': 0
            },
            'status': 'no_data'
        }

    def _get_digital_id_from_member_id(self, member_id: str) -> Optional[str]:
        """从member_id获取digital_id"""
        try:
            sql = """
            SELECT digital_id FROM users
            WHERE member_id = ?
            LIMIT 1
            """
            results = self.db_manager.execute_query(sql, [member_id])
            if results:
                return results[0][0]
            return None
        except Exception as e:
            logger.error(f"从member_id获取digital_id失败: {member_id}, 错误: {str(e)}")
            return None

    def _get_member_id_from_digital_id(self, digital_id: str) -> Optional[str]:
        """从digital_id获取member_id"""
        try:
            sql = """
            SELECT member_id FROM users
            WHERE digital_id = ?
            LIMIT 1
            """
            results = self.db_manager.execute_query(sql, [digital_id])
            if results:
                return results[0][0]
            return None
        except Exception as e:
            logger.error(f"从digital_id获取member_id失败: {digital_id}, 错误: {str(e)}")
            return None

    def find_member_id_by_digital_id(self, digital_id: str, agent_task_id: str = None) -> Optional[str]:
        """通过digital_id查找member_id"""
        try:
            # 直接在用户数据中查找（优先使用users表，因为agent_relationships表可能不存在）
            sql = """
            SELECT DISTINCT member_id FROM users
            WHERE digital_id = ?
            LIMIT 1
            """
            results = self.db_manager.execute_query(sql, [digital_id])
            if results:
                logger.info(f"通过digital_id找到member_id: {digital_id} → {results[0][0]}")
                return results[0][0]

            # 如果users表中没有找到，尝试在共享关系数据中查找
            if agent_task_id:
                try:
                    # 修复表名：使用shared_relationships而不是agent_relationships
                    sql = """
                    SELECT DISTINCT user_a_member_id as member_id FROM shared_relationships
                    WHERE user_a_mid = ? AND task_id = ?
                    UNION
                    SELECT DISTINCT user_b_member_id as member_id FROM shared_relationships
                    WHERE user_b_mid = ? AND task_id = ?
                    LIMIT 1
                    """
                    results = self.db_manager.execute_query(sql, [digital_id, agent_task_id, digital_id, agent_task_id])
                    if results:
                        logger.info(f"通过shared_relationships找到member_id: {digital_id} → {results[0][0]}")
                        return results[0][0]
                except Exception as shared_error:
                    # shared_relationships表查询失败，记录调试信息
                    logger.debug(f"shared_relationships表查询失败: {str(shared_error)}")

            # 尝试直接将digital_id作为member_id使用（某些情况下它们可能相同）
            try:
                sql = """
                SELECT COUNT(*) as count FROM user_trading_profiles
                WHERE member_id = ?
                LIMIT 1
                """
                results = self.db_manager.execute_query(sql, [digital_id])
                if results and results[0][0] > 0:
                    logger.info(f"digital_id直接作为member_id找到: {digital_id}")
                    return digital_id
            except Exception as direct_error:
                logger.debug(f"直接使用digital_id作为member_id查询失败: {str(direct_error)}")

            logger.info(f"未找到digital_id对应的member_id: {digital_id}")
            return None

        except Exception as e:
            logger.error(f"通过digital_id查找member_id失败 - digital_id: {digital_id}, 错误: {str(e)}")
            return None
    
    def get_user_positions(self, user_id: str, task_id: str = '', days: int = 30) -> List[Dict]:
        """获取用户持仓数据 - 从position_analysis表获取实际数据"""
        try:
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 🚀 修复：根据task_id参数构建查询条件，确保结果一致性
            base_sql = """
            SELECT member_id, contract_name as symbol,
                   CASE WHEN primary_side = 1 THEN 'LONG' ELSE 'SHORT' END as position_type,
                   total_open_amount as position_size,
                   avg_open_price as entry_price,
                   avg_close_price as current_price,
                   COALESCE(net_pnl, 0) as pnl,
                   COALESCE(leverage, 1) as leverage,
                   0 as margin_ratio,
                   open_time,
                   close_time,
                   '{}' as additional_data,
                   '' as digital_id,
                   COALESCE(market_orders_open, 0) as market_orders_open,
                   COALESCE(limit_orders_open, 0) as limit_orders_open,
                   COALESCE(market_orders_close, 0) as market_orders_close,
                   COALESCE(limit_orders_close, 0) as limit_orders_close,
                   COALESCE(cross_margin_positions, 0) as cross_margin_positions,
                   COALESCE(isolated_margin_positions, 0) as isolated_margin_positions
            FROM position_analysis
            WHERE member_id = ?
            """

            params = [user_id]

            # 🚀 修复：如果指定了task_id，添加task_id过滤条件
            if task_id and task_id.strip():
                base_sql += " AND task_id = ?"
                params.append(task_id.strip())
                logger.info(f"使用指定的task_id进行查询: {task_id}")
            else:
                logger.info("未指定task_id，查询所有任务的数据")

            # 添加时间范围和排序
            sql = base_sql + """
            AND open_time >= ?
            AND open_time <= ?
            ORDER BY open_time DESC
            LIMIT 1000
            """

            params.extend([start_date.isoformat(), end_date.isoformat()])
            results = self.db_manager.execute_query(sql, params)
            
            if not results:
                logger.info(f"在position_analysis表中未找到用户 {user_id} 的持仓数据（时间范围：{start_date.date()} 到 {end_date.date()}）")
                # 尝试查询所有时间范围的数据
                sql_all = """
                SELECT COUNT(*) as total_count FROM position_analysis WHERE member_id = ?
                """
                total_results = self.db_manager.execute_query(sql_all, [user_id])
                total_count = total_results[0][0] if total_results else 0
                logger.info(f"用户 {user_id} 在position_analysis表中总共有 {total_count} 条记录")
                
                if total_count > 0:
                    # 如果有数据但不在时间范围内，扩大时间范围
                    start_date_extended = end_date - timedelta(days=365)  # 扩大到1年

                    # 🚀 修复：扩展查询也要保持task_id条件一致
                    extended_params = [user_id]
                    if task_id and task_id.strip():
                        extended_params.append(task_id.strip())
                    extended_params.extend([start_date_extended.isoformat(), end_date.isoformat()])

                    results = self.db_manager.execute_query(sql, extended_params)
                    logger.info(f"扩大时间范围后找到 {len(results) if results else 0} 条记录")
                
                if not results:
                    return []
            
            positions = []
            for row in results:
                position = {
                    'member_id': row[0],
                    'symbol': row[1] or 'UNKNOWN',
                    'position_type': row[2] or 'UNKNOWN',
                    'position_size': float(row[3]) if row[3] else 0,
                    'entry_price': float(row[4]) if row[4] else 0,
                    'current_price': float(row[5]) if row[5] else 0,
                    'pnl': float(row[6]) if row[6] else 0,
                    'leverage': float(row[7]) if row[7] else 1,
                    'margin_ratio': float(row[8]) if row[8] else 0,
                    'open_time': row[9],
                    'close_time': row[10],
                    'additional_data': row[11] or '{}',
                    'digital_id': row[12] or '',
                    # 🔧 修复：添加订单类型字段
                    'market_orders_open': int(row[13]) if row[13] else 0,
                    'limit_orders_open': int(row[14]) if row[14] else 0,
                    'market_orders_close': int(row[15]) if row[15] else 0,
                    'limit_orders_close': int(row[16]) if row[16] else 0,
                    'cross_margin_positions': int(row[17]) if row[17] else 0,
                    'isolated_margin_positions': int(row[18]) if row[18] else 0
                }
                positions.append(position)
            
            logger.info(f"成功获取用户 {user_id} 的 {len(positions)} 条持仓数据")
            return positions
            
        except Exception as e:
            logger.error(f"获取用户持仓数据失败 - user_id: {user_id}, 错误: {str(e)}")
            return []
    
    def get_user_trading_profile(self, user_id: str) -> Optional[Dict]:
        """
        从user_trading_profiles表获取用户的完整交易画像数据

        Args:
            user_id: 用户ID

        Returns:
            Dict: 用户交易画像数据，如果不存在返回None
        """
        try:
            # 🔧 修复：使用指定字段查询避免SELECT *的字典转换问题
            # 🔧 修复：优先获取数据完整的记录，避免获取到数据不完整的最新记录
            sql = """
            SELECT
                id, member_id, analysis_date, analysis_period_start, analysis_period_end,
                total_positions, completed_positions, total_volume, total_trades, avg_trade_size, total_commission,
                max_trade_size, min_trade_size, total_pnl, profit_trades_ratio, loss_trades_ratio,
                avg_profit_per_trade, avg_loss_per_trade, return_rate, fee_ratio,
                profitable_count, loss_count, total_profit, total_loss, win_rate, profit_loss_ratio,
                profit_factor, profit_consistency, avg_profit_duration_minutes, avg_loss_duration_minutes,
                profit_loss_duration_ratio, total_trading_days, trading_frequency, max_holding_time, min_holding_time,
                market_orders_ratio, limit_orders_ratio, open_market_orders, open_limit_orders,
                close_market_orders, close_limit_orders, avg_leverage, max_leverage, leverage_stability,
                max_single_loss, max_single_loss_ratio, low_leverage_trades, medium_leverage_trades, high_leverage_trades,
                wash_trading_positions_count, concurrent_positions_count, wash_trading_contracts,
                major_coins_ratio, altcoins_ratio, diversification_score, favorite_contracts, peak_trading_hours,
                defi_percentage, others_percentage, risk_appetite_level, volatility_preference,
                coin_win_rate_analysis, advantage_coins, expert_coins, total_analyzed_coins, avg_coin_win_rate,
                fund_scale_category, real_trading_volume, small_trades, medium_trades, large_trades,
                small_trades_ratio, medium_trades_ratio, large_trades_ratio,
                professional_score, profitability_score, risk_control_score, trading_behavior_score, market_understanding_score,
                win_rate_score, profit_loss_ratio_score, profit_factor_score, profit_consistency_score,
                avg_leverage_score, max_leverage_score, leverage_stability_score, trading_frequency_score,
                market_order_ratio_score, duration_ratio_score, position_consistency_score, timing_ability_score,
                risk_discipline_score, execution_efficiency_score, trader_type, confidence_level,
                abnormal_volume, abnormal_ratio, wash_trading_volume, high_frequency_volume,
                funding_arbitrage_volume, risk_events_count, position_consistency, timing_ability,
                risk_discipline, execution_efficiency, last_activity_time, max_single_loss_score,
                created_at, updated_at
            FROM user_trading_profiles
            WHERE member_id = ?
            ORDER BY
                -- 优先选择数据完整的记录（total_volume > 0 且 advantage_coins 不为空）
                CASE
                    WHEN total_volume > 0 AND advantage_coins IS NOT NULL THEN 0
                    WHEN total_volume > 0 THEN 1
                    ELSE 2
                END,
                analysis_date DESC,
                created_at DESC
            LIMIT 1
            """

            results = self.db_manager.execute_query(sql, [user_id])

            if results:
                # 手动构建字典，确保字段对应正确
                row = results[0]
                profile_data = {
                    'id': row[0], 'member_id': row[1], 'analysis_date': row[2], 'analysis_period_start': row[3], 'analysis_period_end': row[4],
                    'total_positions': row[5], 'completed_positions': row[6], 'total_volume': row[7], 'total_trades': row[8], 'avg_trade_size': row[9], 'total_commission': row[10],
                    'max_trade_size': row[11], 'min_trade_size': row[12], 'total_pnl': row[13], 'profit_trades_ratio': row[14], 'loss_trades_ratio': row[15],
                    'avg_profit_per_trade': row[16], 'avg_loss_per_trade': row[17], 'return_rate': row[18], 'fee_ratio': row[19],
                    'profitable_count': row[20], 'loss_count': row[21], 'total_profit': row[22], 'total_loss': row[23], 'win_rate': row[24], 'profit_loss_ratio': row[25],
                    'profit_factor': row[26], 'profit_consistency': row[27], 'avg_profit_duration_minutes': row[28], 'avg_loss_duration_minutes': row[29],
                    'profit_loss_duration_ratio': row[30], 'total_trading_days': row[31], 'trading_frequency': row[32], 'max_holding_time': row[33], 'min_holding_time': row[34],
                    'market_orders_ratio': row[35], 'limit_orders_ratio': row[36], 'open_market_orders': row[37], 'open_limit_orders': row[38],
                    'close_market_orders': row[39], 'close_limit_orders': row[40], 'avg_leverage': row[41], 'max_leverage': row[42], 'leverage_stability': row[43],
                    'max_single_loss': row[44], 'max_single_loss_ratio': row[45], 'low_leverage_trades': row[46], 'medium_leverage_trades': row[47], 'high_leverage_trades': row[48],
                    'wash_trading_positions_count': row[49], 'concurrent_positions_count': row[50], 'wash_trading_contracts': row[51],
                    'major_coins_ratio': row[52], 'altcoins_ratio': row[53], 'diversification_score': row[54], 'favorite_contracts': row[55], 'peak_trading_hours': row[56],
                    'defi_percentage': row[57], 'others_percentage': row[58], 'risk_appetite_level': row[59], 'volatility_preference': row[60],
                    'coin_win_rate_analysis': row[61], 'advantage_coins': row[62], 'expert_coins': row[63], 'total_analyzed_coins': row[64], 'avg_coin_win_rate': row[65],
                    'fund_scale_category': row[66], 'real_trading_volume': row[67], 'small_trades': row[68], 'medium_trades': row[69], 'large_trades': row[70],
                    'small_trades_ratio': row[71], 'medium_trades_ratio': row[72], 'large_trades_ratio': row[73],
                    'professional_score': row[74], 'profitability_score': row[75], 'risk_control_score': row[76], 'trading_behavior_score': row[77], 'market_understanding_score': row[78],
                    'win_rate_score': row[79], 'profit_loss_ratio_score': row[80], 'profit_factor_score': row[81], 'profit_consistency_score': row[82],
                    'avg_leverage_score': row[83], 'max_leverage_score': row[84], 'leverage_stability_score': row[85], 'trading_frequency_score': row[86],
                    'market_order_ratio_score': row[87], 'duration_ratio_score': row[88], 'position_consistency_score': row[89], 'timing_ability_score': row[90],
                    'risk_discipline_score': row[91], 'execution_efficiency_score': row[92], 'trader_type': row[93], 'confidence_level': row[94],
                    'abnormal_volume': row[95], 'abnormal_ratio': row[96], 'wash_trading_volume': row[97], 'high_frequency_volume': row[98],
                    'funding_arbitrage_volume': row[99], 'risk_events_count': row[100], 'position_consistency': row[101], 'timing_ability': row[102],
                    'risk_discipline': row[103], 'execution_efficiency': row[104], 'last_activity_time': row[105], 'max_single_loss_score': row[106],
                    'created_at': row[107], 'updated_at': row[108]
                }

                logger.info(f"成功从user_trading_profiles表获取用户 {user_id} 的数据")
                return profile_data
            else:
                logger.warning(f"user_trading_profiles表中没有找到用户 {user_id} 的数据")
                return None

        except Exception as e:
            logger.error(f"从user_trading_profiles表获取用户 {user_id} 数据失败: {str(e)}")
            return None
    
    def check_user_trading_profile_exists(self, user_id: str) -> bool:
        """
        检查用户是否在user_trading_profiles表中存在
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否存在
        """
        try:
            sql = """
            SELECT COUNT(*) as count FROM user_trading_profiles
            WHERE member_id = ?
            """
            
            results = self.db_manager.execute_query(sql, [user_id])
            count = results[0][0] if results else 0
            
            return count > 0
            
        except Exception as e:
            logger.error(f"检查用户 {user_id} 是否存在user_trading_profiles表失败: {str(e)}")
            return False


# 创建全局数据仓库实例
data_repository = UnifiedUserDataRepository()