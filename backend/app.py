"""
风险分析系统主应用
模块化架构的统一入口点
"""
from flask import Flask, request, jsonify, send_from_directory, abort, send_file, session, redirect
from flask_cors import CORS
import os
import sys
import logging
from datetime import datetime
import pandas as pd
from io import BytesIO

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 导入各个模块的API
from modules.contract_risk_analysis.api.contract_api import contract_bp
from modules.agent_relationship.api.agent_api import agent_bp
from modules.link_risk_analysis.api.link_api import link_bp
# 用户分析API已整合到unified_user_api.py，通过register_unified_apis注册

# 导入鉴权模块的API
from modules.auth.api.auth_api import auth_bp
from modules.auth.api.user_api import user_bp as auth_user_bp
from modules.auth.api.twofa_api import twofa_bp
from core.utils.decorators import login_required, admin_required

# 导入数据库管理器
from database.duckdb_manager import db_manager
from database.repositories.task_repository import task_repository
from database.api import db_bp

# 导入鉴权配置
from config.auth_settings import SESSION_CONFIG

# 配置简化日志 - 只显示错误和重要统计信息
logging.basicConfig(
    level=logging.WARNING,  # 设置为WARNING级别，减少详细日志
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 设置关键模块的日志级别
logging.getLogger('modules.contract_risk_analysis.services.contract_analyzer').setLevel(logging.WARNING)
logging.getLogger('modules.user_analysis.services.basic_metrics_calculator').setLevel(logging.WARNING)
logging.getLogger('modules.user_analysis.services.derived_metrics_calculator').setLevel(logging.WARNING)
logging.getLogger('modules.user_analysis.services.coin_win_rate_analyzer').setLevel(logging.WARNING)
logging.getLogger('database.duckdb_manager').setLevel(logging.WARNING)  # 减少DuckDB管理器日志
logging.getLogger('werkzeug').setLevel(logging.ERROR)  # Flask HTTP日志
logging.getLogger('flask').setLevel(logging.ERROR)

logger = logging.getLogger(__name__)

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)

    # 基本配置
    app.config['MAX_CONTENT_LENGTH'] = 2 * 1024 * 1024 * 1024  # 2GB

    # 鉴权会话配置
    app.config.update(SESSION_CONFIG)

    # CSRF保护配置
    app.config['CSRF_TOKEN_TIMEOUT'] = 3600  # CSRF token 1小时有效期

    # 初始化自定义CSRF保护
    from core.security import csrf
    csrf.init_app(app)

    # 设置Flask应用的日志级别，减少重复输出
    app.logger.setLevel(logging.WARNING)
    
    # 启用CORS (更新为支持鉴权的配置，支持IP地址访问)
    CORS(app,
         origins=["*"],  # 允许所有来源
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         allow_headers=["Content-Type", "Authorization", "X-Requested-With"],
         supports_credentials=True,  # 支持cookies
         expose_headers=["Content-Type", "Authorization"]
    )

    # 添加安全响应头
    @app.after_request
    def add_security_headers(response):
        """添加安全响应头"""
        # 防止点击劫持
        response.headers['X-Frame-Options'] = 'DENY'
        # 防止MIME类型嗅探
        response.headers['X-Content-Type-Options'] = 'nosniff'
        # XSS保护
        response.headers['X-XSS-Protection'] = '1; mode=block'
        # 内容安全策略（宽松配置以支持现有功能）
        response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'"
        # 引用策略
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        # HTTPS强制（仅在生产环境启用）
        if not app.debug:
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        return response
    
    # 注册鉴权蓝图 (必须最先注册)
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(auth_user_bp, url_prefix='/api/auth')
    app.register_blueprint(twofa_bp, url_prefix='/api/auth')

    # 注册权限管理蓝图
    from modules.auth.api.permission_api import permission_bp
    app.register_blueprint(permission_bp, url_prefix='/api/permissions')

    # 注册配置管理蓝图
    from modules.admin.api.config_management_api import config_bp
    app.register_blueprint(config_bp, url_prefix='/api/config')

    # CSRF token获取接口已在csrf.init_app中注册

    # 注册业务模块蓝图
    app.register_blueprint(contract_bp, url_prefix='/api/contract')
    app.register_blueprint(agent_bp, url_prefix='/api/agent')
    app.register_blueprint(link_bp, url_prefix='/api/link')
    # 注册统一的用户分析API
    from modules.user_analysis.api import register_unified_apis
    register_unified_apis(app)
    app.register_blueprint(db_bp, url_prefix='/api/database')

    # 应用启动时运行任务健康检查
    def startup_health_check():
        """应用启动时的健康检查"""
        try:
            from modules.contract_risk_analysis.services.task_health_monitor import task_health_monitor
            logger.info("执行启动时任务健康检查...")
            results = task_health_monitor.run_health_check(auto_fix=True)

            if results['recovered_count'] > 0 or results['fixed_count'] > 0:
                logger.info(f"启动健康检查完成: 恢复了 {results['recovered_count']} 个孤儿任务, "
                           f"修复了 {results['fixed_count']} 个卡住任务")
            else:
                logger.info("启动健康检查完成: 所有任务状态正常")

        except Exception as e:
            logger.error(f"启动健康检查失败: {e}")

    # 在应用启动后运行健康检查
    import threading
    health_check_thread = threading.Thread(target=startup_health_check)
    health_check_thread.daemon = True
    health_check_thread.start()

    # 健康检查端点 - 公开基础信息
    @app.route('/health')
    def health_check():
        """健康检查端点 - 仅返回基础状态信息"""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'service': 'risk-analysis-system'
        })

    # API健康检查端点 - 公开基础信息
    @app.route('/api/health')
    def api_health_check():
        """API健康检查端点 - 仅返回基础状态信息"""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'service': 'risk-analysis-api'
        })

    # 详细健康检查端点 - 需要登录权限
    @app.route('/api/health/detailed')
    @login_required
    def detailed_health_check():
        """详细健康检查端点 - 需要登录权限"""
        try:
            # 获取数据库统计信息
            db_stats = db_manager.get_table_stats()
            db_status = 'connected'
        except Exception as e:
            logger.error(f"数据库健康检查失败: {str(e)}")
            db_stats = {'error': '数据库连接异常'}
            db_status = 'error'

        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'modules': {
                'contract_risk_analysis': 'active',
                'agent_relationship': 'active',
                'link_risk_analysis': 'active',
                'user_analysis': 'active'
            },
            'database': {
                'status': db_status,
                'statistics': db_stats
            },
            'user': {
                'id': session.get('user_id'),
                'username': session.get('username'),
                'role': session.get('role')
            }
        })
    
    # 静态文件服务 - 指向构建后的dist目录
    @app.route('/')
    def index():
        """主页面"""
        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist')
            return send_from_directory(dist_path, 'index.html')
        except:
            return send_from_directory('../frontend/dist', 'index.html')
    
    # 登录页面路由
    @app.route('/login')
    @app.route('/login.html')
    def login():
        """登录页面"""
        try:
            # 优先从frontend源码目录提供登录页面
            frontend_path = os.path.join(project_root, '..', 'frontend', 'src', 'auth')
            if os.path.exists(os.path.join(frontend_path, 'login.html')):
                return send_from_directory(frontend_path, 'login.html')
            else:
                return send_from_directory('../frontend/dist', 'login.html')
        except Exception as e:
            logger.error(f"登录页面加载失败: {str(e)}")
            return "登录页面未找到", 404
    
    # 2FA验证页面路由 - 支持GET和POST请求
    @app.route('/2fa-verify.html', methods=['GET', 'POST'])
    @app.route('/2fa-verify', methods=['GET', 'POST'])
    @app.route('/frontend/src/auth/2fa-verify.html', methods=['GET', 'POST'])
    def twofa_verify():
        """2FA验证页面 - 安全处理POST传递的敏感数据"""
        try:
            # 获取页面路径
            frontend_path = os.path.join(project_root, '..', 'frontend', 'src', 'auth')
            page_path = None

            if os.path.exists(os.path.join(frontend_path, '2fa-verify.html')):
                page_path = os.path.join(frontend_path, '2fa-verify.html')
            else:
                page_path = os.path.join('../frontend/dist', '2fa-verify.html')

            # 如果是POST请求，处理敏感数据
            if request.method == 'POST':
                temp_token = request.form.get('temp_token')
                username = request.form.get('username')
                setup = request.form.get('setup', 'false')

                # 验证必要参数
                if not temp_token or not username:
                    logger.warning("2FA页面POST请求缺少必要参数")
                    return "参数错误", 400

                # 读取HTML文件内容
                with open(page_path, 'r', encoding='utf-8') as f:
                    html_content = f.read()

                # 将敏感数据安全地注入到页面中（通过JavaScript变量）
                is_setup_js = 'true' if setup.lower() == 'true' else 'false'
                secure_data_script = f"""
                <script>
                    // 安全地设置敏感数据，避免URL暴露
                    window.secureAuthData = {{
                        tempToken: '{temp_token}',
                        username: '{username}',
                        isSetup: {is_setup_js}
                    }};
                </script>
                """

                # 在</head>标签前插入脚本
                html_content = html_content.replace('</head>', secure_data_script + '\n</head>')

                return html_content

            else:
                # GET请求，正常返回页面（向后兼容）
                if os.path.exists(os.path.join(frontend_path, '2fa-verify.html')):
                    return send_from_directory(frontend_path, '2fa-verify.html')
                else:
                    return send_from_directory('../frontend/dist', '2fa-verify.html')

        except Exception as e:
            logger.error(f"2FA验证页面加载失败: {str(e)}")
            return "2FA验证页面未找到", 404

    # 鉴权测试页面路由
    @app.route('/test-auth.html')
    @app.route('/test-auth')
    def test_auth():
        """鉴权测试页面"""
        try:
            frontend_path = os.path.join(project_root, '..', 'frontend', 'src', 'auth')
            return send_from_directory(frontend_path, 'test-auth.html')
        except Exception as e:
            logger.error(f"测试页面加载失败: {str(e)}")
            return "测试页面未找到", 404
    
    # 页面路由保护函数
    def check_page_access():
        """检查页面访问权限"""
        # 检查是否已登录
        if 'user_id' not in session or 'username' not in session:
            return redirect('/login')

        # 验证会话是否有效
        from modules.auth.services.session_manager import session_manager
        if not session_manager.validate_session(session.get('session_id')):
            session.clear()
            return redirect('/login')

        return None  # 通过验证

    @app.route('/agent_relationship.html')
    @app.route('/agent-relationship')
    def agent_relationship():
        """代理关系分析页面 - 需要登录"""
        # 检查访问权限
        auth_check = check_page_access()
        if auth_check:
            return auth_check

        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist')
            return send_from_directory(dist_path, 'agent_relationship.html')
        except:
            return send_from_directory('../frontend/dist', 'agent_relationship.html')

    @app.route('/contract_analysis.html')
    @app.route('/contract-analysis')
    def contract_analysis():
        """合约分析页面 - 需要登录"""
        # 检查访问权限
        auth_check = check_page_access()
        if auth_check:
            return auth_check

        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist')
            return send_from_directory(dist_path, 'contract_analysis.html')
        except:
            return send_from_directory('../frontend/dist', 'contract_analysis.html')
    

    
    @app.route('/contract_integration.html')
    @app.route('/contract-integration')
    def contract_integration():
        """合约整合页面 - 需要登录"""
        # 检查访问权限
        auth_check = check_page_access()
        if auth_check:
            return auth_check

        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist')
            return send_from_directory(dist_path, 'contract_integration.html')
        except:
            return send_from_directory('../frontend/dist', 'contract_integration.html')

    @app.route('/user_analysis.html')
    @app.route('/user-analysis')
    def user_analysis():
        """用户分析页面 - 需要登录"""
        # 检查访问权限
        auth_check = check_page_access()
        if auth_check:
            return auth_check

        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist')
            return send_from_directory(dist_path, 'user_analysis.html')
        except:
            return send_from_directory('../frontend/dist', 'user_analysis.html')

    # 静态资源安全检查函数
    def is_safe_static_file(filename):
        """检查静态文件是否安全"""
        # 允许的文件扩展名
        allowed_extensions = {'.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot', '.map'}

        # 禁止的文件名模式
        forbidden_patterns = ['config', 'secret', 'key', 'password', '.env', 'database']

        # 获取文件扩展名
        _, ext = os.path.splitext(filename.lower())

        # 检查扩展名
        if ext not in allowed_extensions:
            return False

        # 检查文件名是否包含敏感词
        filename_lower = filename.lower()
        for pattern in forbidden_patterns:
            if pattern in filename_lower:
                return False

        # 检查路径遍历攻击
        if '..' in filename or filename.startswith('/'):
            return False

        return True

    # 静态资源服务 - 包括构建后的js和css文件
    @app.route('/js/<path:filename>')
    def js_files(filename):
        """JavaScript文件服务 - 安全检查"""
        # 安全检查
        if not is_safe_static_file(filename):
            logger.warning(f"拒绝访问不安全的JS文件: {filename}, IP: {request.remote_addr}")
            abort(403)

        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist', 'js')
            return send_from_directory(dist_path, filename)
        except:
            return send_from_directory('../frontend/dist/js', filename)

    @app.route('/css/<path:filename>')
    def css_files(filename):
        """CSS文件服务 - 安全检查"""
        # 安全检查
        if not is_safe_static_file(filename):
            logger.warning(f"拒绝访问不安全的CSS文件: {filename}, IP: {request.remote_addr}")
            abort(403)

        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist', 'css')
            return send_from_directory(dist_path, filename)
        except:
            return send_from_directory('../frontend/dist/css', filename)

    @app.route('/assets/<path:filename>')
    def assets_files(filename):
        """资源文件服务 - 安全检查"""
        # 安全检查
        if not is_safe_static_file(filename):
            logger.warning(f"拒绝访问不安全的资源文件: {filename}, IP: {request.remote_addr}")
            abort(403)

        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist', 'assets')
            return send_from_directory(dist_path, filename)
        except:
            return send_from_directory('../frontend/dist/assets', filename)
    
    # 鉴权相关静态文件路由 - 增强安全检查
    @app.route('/frontend/src/auth/<path:filename>')
    def auth_files(filename):
        """鉴权相关文件服务 - 增强安全检查"""
        # 安全检查
        if not is_safe_static_file(filename):
            logger.warning(f"拒绝访问不安全的鉴权文件: {filename}, IP: {request.remote_addr}")
            abort(403)

        try:
            auth_path = os.path.join(project_root, '..', 'frontend', 'src', 'auth')
            return send_from_directory(auth_path, filename)
        except Exception as e:
            logger.error(f"鉴权文件加载失败: {filename}, 错误: {str(e)}")
            abort(404)
    
    @app.route('/frontend/src/shared/utils/<path:filename>')
    def auth_utils_files(filename):
        """鉴权工具文件服务 - 增强安全检查"""
        # 安全检查
        if not is_safe_static_file(filename):
            logger.warning(f"拒绝访问不安全的工具文件: {filename}, IP: {request.remote_addr}")
            abort(403)

        try:
            utils_path = os.path.join(project_root, '..', 'frontend', 'src', 'shared', 'utils')
            return send_from_directory(utils_path, filename)
        except Exception as e:
            logger.error(f"工具文件加载失败: {filename}, 错误: {str(e)}")
            abort(404)
    
    @app.route('/static/<path:filename>')
    def static_files(filename):
        """静态文件服务 - 向后兼容，增强安全检查"""
        # 安全检查
        if not is_safe_static_file(filename):
            logger.warning(f"拒绝访问不安全的静态文件: {filename}, IP: {request.remote_addr}")
            abort(403)

        static_dirs = [
            'agent_analyzer/static',  # 添加dist目录支持
            '../frontend/src/shared',
            '../frontend/css',
            '../frontend/lib'
        ]

        for static_dir in static_dirs:
            full_path = os.path.join(project_root, static_dir, filename)
            if os.path.exists(full_path):
                return send_from_directory(os.path.dirname(full_path), os.path.basename(full_path))

        abort(404)
    
    # 获取任务列表API - 根据用户权限过滤
    @app.route('/api/tasks', methods=['GET'])
    @login_required
    def get_tasks():
        """获取任务列表，根据用户权限过滤可见任务"""
        try:
            # 获取当前用户信息
            current_user_role = session.get('role', 'viewer')
            current_user_id = session.get('user_id')

            # 获取基本任务列表
            all_tasks = task_repository.get_all_tasks()

            # 根据用户权限过滤任务
            if current_user_role == 'admin':
                # 管理员可以看到所有任务
                tasks = all_tasks
            else:
                # 普通用户只能看到已完成的任务和基本信息
                tasks = []
                for task in all_tasks:
                    # 只显示已完成的任务，并过滤敏感信息
                    if task.get('status') == 'completed':
                        filtered_task = {
                            'task_id': task.get('task_id', ''),
                            'name': task.get('name', ''),
                            'type': task.get('type', ''),
                            'status': task.get('status', ''),
                            'created_at': task.get('created_at', ''),
                            'description': '已完成的分析任务',  # 隐藏详细描述
                            'file_count': 0,  # 隐藏文件数量
                            'data_count': 0   # 隐藏数据数量
                        }
                        tasks.append(filtered_task)

            # 按类型分类任务
            contract_tasks = []
            agent_tasks = []
            other_tasks = []

            for task in tasks:
                task_data = {
                    'task_id': task.get('task_id', ''),
                    'name': task.get('name', ''),
                    'type': task.get('type', ''),
                    'status': task.get('status', ''),
                    'created_at': task.get('created_at', ''),
                    'description': task.get('description', ''),
                    'file_count': task.get('file_count', 0),
                    'data_count': task.get('data_count', 0)
                }

                if 'contract' in task.get('type', '').lower() or 'risk' in task.get('type', '').lower():
                    contract_tasks.append(task_data)
                elif 'agent' in task.get('type', '').lower() or 'relationship' in task.get('type', '').lower():
                    agent_tasks.append(task_data)
                else:
                    other_tasks.append(task_data)

            # 构建增强响应
            response_data = {
                'status': 'success',
                'total_tasks': len(tasks),
                'tasks': tasks,  # 保持原有格式兼容性
                'classified_tasks': {
                    'contract_tasks': contract_tasks,
                    'agent_tasks': agent_tasks,
                    'other_tasks': other_tasks
                },
                'task_options': {
                    'contract_task_options': [
                        {'value': task['task_id'], 'label': f"{task['name']} ({task['status']})"}
                        for task in contract_tasks
                    ],
                    'agent_task_options': [
                        {'value': task['task_id'], 'label': f"{task['name']} ({task['status']})"}
                        for task in agent_tasks
                    ]
                },
                'summary': {
                    'contract_tasks_count': len(contract_tasks),
                    'agent_tasks_count': len(agent_tasks),
                    'other_tasks_count': len(other_tasks),
                    'completed_tasks': len([t for t in tasks if t.get('status') == 'completed']),
                    'running_tasks': len([t for t in tasks if t.get('status') == 'running']),
                    'failed_tasks': len([t for t in tasks if t.get('status') == 'failed'])
                },
                'user_permissions': {
                    'role': current_user_role,
                    'can_view_all_tasks': current_user_role == 'admin',
                    'filtered_view': current_user_role != 'admin'
                }
            }

            return jsonify(response_data)
            
        except Exception as e:
            logger.error(f"获取任务列表失败: {str(e)}")
            return jsonify({
                'status': 'error',
                'error': str(e),
                'tasks': [],
                'classified_tasks': {
                    'contract_tasks': [],
                    'agent_tasks': [],
                    'other_tasks': []
                },
                'task_options': {
                    'contract_task_options': [],
                    'agent_task_options': []
                }
            }), 500

    # 通用任务状态API
    @app.route('/api/task/<task_id>', methods=['GET'])
    @login_required
    def get_task_status(task_id):
        """获取任务状态"""
        try:
            # 从DuckDB获取任务状态
            task = task_repository.get_task(task_id)
            
            if task:
                return jsonify({
                    'task_id': task_id,
                    'status': task.get('status'),
                    'message': task.get('message', ''),
                    'progress': task.get('progress', 0),
                    'created_at': task.get('created_at'),
                    'updated_at': task.get('updated_at'),
                    'completed_at': task.get('completed_at')
                })
            
            # 如果没找到，返回任务不存在
            return jsonify({'error': '任务不存在', 'task_id': task_id}), 404
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500



    @app.route('/api/task/<task_id>/result', methods=['GET'])
    @login_required
    def get_task_result(task_id):
        """获取任务结果"""
        try:
            # 获取分页参数
            page = int(request.args.get('page', 1))
            page_size = int(request.args.get('page_size', 100))
            
            # 参数验证
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 1000:
                page_size = 100
            
            # 从DuckDB获取任务信息
            task = task_repository.get_task(task_id)
            
            if not task:
                return jsonify({'error': '任务不存在', 'task_id': task_id}), 404
            
            # 根据任务类型获取结果
            task_type = task.get('task_type', '')
            result_summary = task.get('result_summary', {})
            
            if task_type == 'contract_analysis':
                # 🚀 修改：使用数据适配器智能路由到新存储
                from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
                adapter = ContractDataAdapter()
                analysis_result = adapter.get_analysis_result(task_id)
                
                if analysis_result:
                    result_data = analysis_result.get('result_data', {})
                    return jsonify({
                        'task_id': task_id,
                        'task_type': task_type,
                        'status': task.get('status'),
                        'message': task.get('message', ''),
                        'created_at': task.get('created_at'),
                        'updated_at': task.get('updated_at'),
                        'result': result_data,
                        'page': page,
                        'page_size': page_size,
                        'error': None
                    })
            
            # 其他任务类型返回基本信息
            return jsonify({
                'task_id': task_id,
                'task_type': task_type,
                'status': task.get('status'),
                'message': task.get('message', ''),
                'created_at': task.get('created_at'),
                'updated_at': task.get('updated_at'),
                'result': result_summary,
                'page': page,
                'page_size': page_size,
                'error': None
            })

        except Exception as e:
            # 记录详细错误日志
            app.logger.error(f"获取任务结果出错: {str(e)}", exc_info=True)
            # 返回友好的错误消息
            return jsonify({
                "error": f"获取任务结果失败: {str(e)}",
                "task_id": task_id,
                "status": "error"
            }), 500
    
    @app.route('/api/contract/export/<task_id>', methods=['GET'])
    @admin_required  # 🔒 升级为管理员权限
    def export_contract_data(task_id):
        """导出合约分析结果为Excel文件"""
        try:
            logger.info(f"导出合约分析结果: {task_id}")
            
            # 🚀 修改：使用数据适配器智能路由到新存储
            from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
            adapter = ContractDataAdapter()
            analysis_result = adapter.get_analysis_result(task_id)
            
            if not analysis_result:
                return jsonify({'error': '分析结果不存在'}), 404
            
            # 获取分析结果数据
            result_data = analysis_result.get('result_data', {})
            contract_risks = result_data.get('contract_risks', [])
            
            if not contract_risks:
                return jsonify({'error': '没有可导出的风险数据'}), 404
            
            # 创建DataFrame
            df = pd.DataFrame(contract_risks)
            
            # 生成Excel文件
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='合约风险分析', index=False)
            
            output.seek(0)
            
            # 生成文件名
            filename = f"contract_risk_analysis_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )
            
        except Exception as e:
            logger.error(f"导出失败: {str(e)}")
            return jsonify({'error': f'导出失败: {str(e)}'}), 500


    @app.route('/api/contract/wash-trading/export/<task_id>', methods=['GET'])
    @admin_required  # 🔒 升级为管理员权限
    def export_wash_trading_data(task_id):
        """导出对敲交易分析结果为Excel文件"""
        try:
            logger.info(f"导出对敲交易分析结果: {task_id}")
            
            # 🚀 修改：使用数据适配器智能路由到新存储
            from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
            adapter = ContractDataAdapter()
            analysis_result = adapter.get_analysis_result(task_id)
            
            if not analysis_result:
                return jsonify({'error': '分析结果不存在'}), 404
            
            # 获取对敲交易数据
            result_data = analysis_result.get('result_data', {})
            contract_risks = result_data.get('contract_risks', [])
            
            # 筛选对敲交易数据
            wash_trading_data = [
                risk for risk in contract_risks 
                if risk.get('detection_type') == 'wash_trading'
            ]
            
            if not wash_trading_data:
                return jsonify({'error': '没有可导出的对敲交易数据'}), 404
            
            # 创建DataFrame
            df = pd.DataFrame(wash_trading_data)
            
            # 生成Excel文件
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='对敲交易分析', index=False)
            
            output.seek(0)
            
            # 生成文件名
            filename = f"wash_trading_analysis_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )
            
        except Exception as e:
            logger.error(f"导出对敲交易数据失败: {str(e)}")
            return jsonify({'error': f'导出失败: {str(e)}'}), 500
    
    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'Resource not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        import traceback
        error_details = traceback.format_exc()
        app.logger.error(f"Internal server error: {error_details}")
        return jsonify({
            'error': 'Internal server error',
            'details': str(error),
            'traceback': error_details
        }), 500
    
    return app

if __name__ == '__main__':
    app = create_app()
    
    # 确保必要的目录存在
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('cache', exist_ok=True)
    os.makedirs('temp', exist_ok=True)

    # 启动Token清理服务
    try:
        from modules.auth.services.token_cleanup_service import token_cleanup_service
        token_cleanup_service.start_cleanup_service()
        logger.info("Token清理服务已启动")
    except Exception as e:
        logger.error(f"启动Token清理服务失败: {e}")

    logger.info("启动风险分析系统...")
    logger.info("系统架构: 模块化设计")
    logger.info("端口: 5005")
    
    port = int(os.environ.get('PORT', 5005))
    app.run(
        host='0.0.0.0',
        port=port,
        debug=False,  # 关闭调试模式避免重复启动
        threaded=True
    )
