-- =============================================
-- 合约风险分析算法结果表结构定义
-- 版本: 2.0 - 拆分存储优化版本
-- =============================================

-- 1. 通用算法结果表
CREATE TABLE IF NOT EXISTS algorithm_results (
    id INTEGER PRIMARY KEY,
    task_id VARCHAR(50) NOT NULL,
    algorithm_type VARCHAR(50) NOT NULL, -- suspected_wash_trading, high_frequency_trading, etc.
    contract_name VARCHAR(100),
    user_id VARCHAR(50),
    risk_level VARCHAR(20), -- HIGH, MEDIUM, LOW
    confidence_score DECIMAL(5,4), -- 0.0000 - 1.0000
    trading_volume DECIMAL(20,8),
    trading_frequency INTEGER,
    time_window_start TIMESTAMP,
    time_window_end TIMESTAMP,
    indicators JSON, -- 存储算法指标
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 对敲交易统一管理表
CREATE TABLE IF NOT EXISTS wash_trading_results (
    id INTEGER PRIMARY KEY,
    algorithm_result_id INTEGER NOT NULL,
    trading_type VARCHAR(20) NOT NULL, -- same_account, cross_account
    pair_count INTEGER DEFAULT 0, -- 交易对数量
    total_volume DECIMAL(20,8),
    avg_time_gap INTEGER, -- 平均时间间隔(秒)
    risk_score DECIMAL(5,4),
    detection_method VARCHAR(50), -- 检测方法
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 同账户对敲详情表
CREATE TABLE IF NOT EXISTS same_account_wash_trading (
    id INTEGER PRIMARY KEY,
    wash_trading_id INTEGER NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(100) NOT NULL,
    long_position_id VARCHAR(50),
    short_position_id VARCHAR(50),
    long_open_time TIMESTAMP,
    short_open_time TIMESTAMP,
    long_volume DECIMAL(20,8),
    short_volume DECIMAL(20,8),
    long_price DECIMAL(20,8),
    short_price DECIMAL(20,8),
    time_gap INTEGER, -- 开仓时间间隔(秒)
    volume_ratio DECIMAL(5,4), -- 成交量比例
    price_correlation DECIMAL(5,4), -- 价格相关性
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. 跨账户对敲详情表
CREATE TABLE IF NOT EXISTS cross_account_wash_trading (
    id INTEGER PRIMARY KEY,
    wash_trading_id INTEGER NOT NULL,
    user_a_id VARCHAR(50) NOT NULL,
    user_b_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(100) NOT NULL,
    bd_relationship VARCHAR(20), -- same_bd, different_bd
    trade_a_id VARCHAR(50),
    trade_b_id VARCHAR(50),
    trade_a_side INTEGER, -- 1=开多, 3=开空
    trade_b_side INTEGER,
    trade_a_time TIMESTAMP,
    trade_b_time TIMESTAMP,
    trade_a_volume DECIMAL(20,8),
    trade_b_volume DECIMAL(20,8),
    trade_a_price DECIMAL(20,8),
    trade_b_price DECIMAL(20,8),
    time_correlation DECIMAL(5,4), -- 时间相关性
    volume_correlation DECIMAL(5,4), -- 成交量相关性
    price_correlation DECIMAL(5,4), -- 价格相关性
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. 高频交易详情表
CREATE TABLE IF NOT EXISTS high_frequency_trading_details (
    id INTEGER PRIMARY KEY,
    algorithm_result_id INTEGER NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(100) NOT NULL,
    trade_count INTEGER,
    avg_holding_time INTEGER, -- 平均持仓时间(秒)
    max_frequency DECIMAL(10,4), -- 最大交易频率(次/分钟)
    volume_concentration DECIMAL(5,4), -- 成交量集中度
    time_pattern VARCHAR(100), -- 时间模式
    risk_indicators JSON, -- 其他风险指标
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


-- 7. 资金费率套利详情表
CREATE TABLE IF NOT EXISTS funding_rate_arbitrage_details (
    id INTEGER PRIMARY KEY,
    algorithm_result_id INTEGER NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(100) NOT NULL,
    funding_rate DECIMAL(10,8), -- 资金费率
    position_size DECIMAL(20,8), -- 仓位大小
    holding_duration INTEGER, -- 持仓时长(秒)
    estimated_profit DECIMAL(20,8), -- 预估收益
    risk_exposure DECIMAL(5,4), -- 风险敞口
    market_conditions JSON, -- 市场条件
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. 合约风险详情表
CREATE TABLE IF NOT EXISTS contract_risk_details (
    id INTEGER PRIMARY KEY,
    algorithm_result_id INTEGER NOT NULL,
    member_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(100),
    detection_type VARCHAR(50), -- suspected_wash_trading, high_frequency_trading, etc.
    detection_method VARCHAR(50),
    risk_level VARCHAR(20), -- HIGH, MEDIUM, LOW
    risk_score DECIMAL(10,4), -- 风险分数
    abnormal_volume DECIMAL(20,8), -- 异常成交量
    trade_count INTEGER, -- 交易次数
    time_range VARCHAR(100), -- 时间范围
    counterparty_ids VARCHAR(500), -- 对手方ID列表
    additional_data TEXT DEFAULT NULL, -- JSON格式的其他数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (algorithm_result_id) REFERENCES algorithm_results(id)
);

-- 9. 仓位分析表
CREATE TABLE IF NOT EXISTS position_analysis (
    position_id VARCHAR(100) PRIMARY KEY,
    member_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(50) NOT NULL,
    primary_side INTEGER NOT NULL,
    open_time TIMESTAMP NOT NULL,
    close_time TIMESTAMP,
    duration_minutes DECIMAL(10,2),
    total_open_amount DECIMAL(15,2),
    total_close_amount DECIMAL(15,2),
    avg_open_price DECIMAL(15,4),
    avg_close_price DECIMAL(15,4),
    total_pnl DECIMAL(15,2),
    total_commission DECIMAL(15,2),
    net_pnl DECIMAL(15,2),
    leverage DECIMAL(8,2),
    market_orders_open INTEGER DEFAULT 0,
    limit_orders_open INTEGER DEFAULT 0,
    market_orders_close INTEGER DEFAULT 0,
    limit_orders_close INTEGER DEFAULT 0,
    cross_margin_positions INTEGER DEFAULT 0,
    isolated_margin_positions INTEGER DEFAULT 0,
    trade_sequence TEXT DEFAULT NULL,  -- 🚀 新增：交易时间序列 [(side, interval_from_prev, amount), ...]
    task_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 10. 对敲交易对详情表
CREATE TABLE IF NOT EXISTS wash_trading_pairs (
    id INTEGER PRIMARY KEY,
    result_id INTEGER NOT NULL,
    pair_index INTEGER NOT NULL,
    user_a_id VARCHAR(50) NOT NULL,
    user_b_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(50) NOT NULL,
    severity VARCHAR(20) DEFAULT 'Medium',
    risk_score DECIMAL(5,4) DEFAULT 0.5000,
    user_a_position_id VARCHAR(100),
    user_a_open_time TIMESTAMP,
    user_a_open_side INTEGER,
    user_a_open_amount DECIMAL(15,2),
    user_a_close_time TIMESTAMP,
    user_a_close_side INTEGER,
    user_a_close_amount DECIMAL(15,2),
    user_a_profit DECIMAL(15,2),
    user_b_position_id VARCHAR(100),
    user_b_open_time TIMESTAMP,
    user_b_open_side INTEGER,
    user_b_open_amount DECIMAL(15,2),
    user_b_close_time TIMESTAMP,
    user_b_close_side INTEGER,
    user_b_close_amount DECIMAL(15,2),
    user_b_profit DECIMAL(15,2),
    open_time_diff_seconds INTEGER,
    close_time_diff_seconds INTEGER,
    total_amount DECIMAL(15,2),
    net_profit DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 11. 未完成仓位等待表
CREATE TABLE IF NOT EXISTS incomplete_positions_waiting (
    position_id VARCHAR(100) PRIMARY KEY,
    member_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(50) NOT NULL,
    primary_side INTEGER NOT NULL CHECK (primary_side IN (1, 3)),
    first_open_time TIMESTAMP NOT NULL,
    total_open_amount DECIMAL(15,2) NOT NULL CHECK (total_open_amount > 0),
    open_trades_count INTEGER DEFAULT 1,
    avg_open_price DECIMAL(15,8) DEFAULT 0,
    total_open_volume DECIMAL(15,2) DEFAULT 0,
    last_close_time TIMESTAMP NULL,
    total_close_amount DECIMAL(15,2) DEFAULT 0,
    close_trades_count INTEGER DEFAULT 0,
    trade_sequence TEXT DEFAULT NULL,  -- 🚀 新增：交易时间序列 [(side, interval_from_prev, amount), ...]
    waiting_since TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_check_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    check_count INTEGER DEFAULT 0,
    source_task_id VARCHAR(50),
    data_version VARCHAR(10) DEFAULT '1.0',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 数据迁移和兼容性视图
-- =============================================

-- 注意：contract_risk_analysis_view 视图已被移除
-- 原因：该视图未被任何代码实际使用，仅作为兼容性设计但未实际调用
-- 如需类似功能，可直接查询 algorithm_results 表并进行相应的聚合操作
