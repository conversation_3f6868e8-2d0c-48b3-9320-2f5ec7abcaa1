#!/bin/bash

# DuckDB数据库管理脚本
# 用于管理项目中的DuckDB数据库

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置 - 使用统一的数据库配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 从Python配置文件获取路径配置
get_db_config() {
    python3 -c "
import sys
import os
sys.path.insert(0, '$SCRIPT_DIR/backend')
from config.database_config import db_config
print(f'DB_PATH={db_config.get_main_db_path()}')
print(f'BACKUP_DIR={db_config.get_backup_dir()}')
print(f'LOG_FILE={db_config.get_log_file()}')
"
}

# 获取配置并设置变量
eval $(get_db_config)

# 确保必要目录存在
mkdir -p "$BACKUP_DIR"
mkdir -p "$(dirname "$LOG_FILE")"

# 日志函数
log() {
    # 检查日志文件是否可写，如果不可写则跳过日志记录
    if [ -w "$(dirname "$LOG_FILE")" ] 2>/dev/null || [ -w "$LOG_FILE" ] 2>/dev/null; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE" 2>/dev/null || true
    fi
}

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    log "INFO: $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    log "SUCCESS: $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    log "WARNING: $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    log "ERROR: $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}    DuckDB 数据库管理工具${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# 检查Python和DuckDB库是否可用
check_duckdb() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装。请先安装 Python3。"
        exit 1
    fi

    # 检查DuckDB Python库是否可用
    python3 -c "import duckdb" 2>/dev/null
    if [ $? -ne 0 ]; then
        print_error "DuckDB Python库未安装。请运行: pip install duckdb"
        exit 1
    fi
}

# 检查数据库文件是否存在
check_db_exists() {
    if [ ! -f "$DB_PATH" ]; then
        print_warning "数据库文件不存在: $DB_PATH"

        # 先检查是否有其他数据库文件
        print_info "检查是否存在其他数据库文件..."
        python3 "$SCRIPT_DIR/manage_duckbd/db_validator_cli.py" scan

        read -p "是否创建新的数据库文件? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            create_database
        else
            print_error "操作取消"
            exit 1
        fi
    fi
}

# 验证数据库路径
validate_database_path() {
    print_info "验证数据库路径: $DB_PATH"
    python3 "$SCRIPT_DIR/manage_duckbd/db_validator_cli.py" validate "$DB_PATH"
}

# 数据库验证和清理菜单
manage_database_validation() {
    echo -e "${YELLOW}数据库验证和清理:${NC}"
    echo "1. 显示数据库配置"
    echo "2. 验证当前数据库路径"
    echo "3. 扫描所有数据库文件"
    echo "4. 清理无效数据库文件"
    echo "5. 生成数据库报告"
    echo "6. 返回主菜单"
    echo
    read -p "请选择操作 (1-6): " -n 1 -r
    echo

    case $REPLY in
        1)
            print_info "显示数据库配置..."
            python3 "$SCRIPT_DIR/manage_duckbd/db_validator_cli.py" config
            ;;
        2)
            validate_database_path
            ;;
        3)
            print_info "扫描数据库文件..."
            python3 "$SCRIPT_DIR/manage_duckbd/db_validator_cli.py" scan
            ;;
        4)
            print_warning "清理无效数据库文件"
            print_info "首先预览可清理的文件..."
            python3 "$SCRIPT_DIR/manage_duckbd/db_validator_cli.py" cleanup
            echo
            read -p "是否执行清理操作? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                python3 "$SCRIPT_DIR/manage_duckbd/db_validator_cli.py" cleanup --force
                print_success "清理操作完成"
            else
                print_info "清理操作已取消"
            fi
            ;;
        5)
            print_info "生成数据库报告..."
            python3 "$SCRIPT_DIR/manage_duckbd/db_validator_cli.py" scan > "$SCRIPT_DIR/data/database_report.txt"
            print_success "报告已保存到: $SCRIPT_DIR/data/database_report.txt"
            cat "$SCRIPT_DIR/data/database_report.txt"
            ;;
        6)
            return
            ;;
        *)
            print_error "无效选择"
            ;;
    esac
}

# 创建数据库
create_database() {
    print_info "创建新的数据库文件..."
    mkdir -p "$(dirname "$DB_PATH")"

    # 使用独立的Python脚本初始化数据库
    python3 "$SCRIPT_DIR/manage_duckbd/create_database.py" "$DB_PATH"

    if [ $? -eq 0 ]; then
        print_success "数据库创建成功: $DB_PATH"

        # 创建完整的user_trading_profiles表结构
        print_info "初始化完整的113字段表结构..."
        create_complete_user_trading_profiles_table
    else
        print_error "数据库创建失败"
        exit 1
    fi
}

# 创建完整的user_trading_profiles表(113字段)
create_complete_user_trading_profiles_table() {
    local sql_file="$SCRIPT_DIR/backend/database/schema/user_trading_profiles_unified.sql"

    if [ ! -f "$sql_file" ]; then
        print_error "找不到表结构文件：$sql_file"
        return 1
    fi

    print_info "使用统一SQL文件创建完整的user_trading_profiles表..."

    python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 读取SQL文件
    with open('$sql_file', 'r', encoding='utf-8') as f:
        sql_content = f.read()

    # 执行SQL脚本
    db_manager.execute_script(sql_content)

    # 验证表结构
    schema = db_manager.execute_sql('DESCRIBE user_trading_profiles')
    field_count = len(schema) if schema else 0

    print(f'✅ user_trading_profiles表创建成功')
    print(f'📊 字段数量: {field_count}')

    if field_count == 113:
        print(f'🎉 完美！包含完整的113个字段')
    elif field_count >= 110:
        print(f'⚠️  基本完整：{field_count}/113 字段')
    elif field_count >= 100:
        print(f'⚠️  字段较完整：{field_count}/113 字段')
    else:
        print(f'❌ 字段不完整：{field_count}/113 字段')

except Exception as e:
    print(f'❌ 创建表失败: {e}')
    import traceback
    traceback.print_exc()
    sys.exit(1)
"

    if [ $? -eq 0 ]; then
        print_success "完整的user_trading_profiles表(113字段)创建成功"
    else
        print_error "user_trading_profiles表创建失败"
        return 1
    fi
}

# 备份数据库
backup_database() {
    check_db_exists

    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="$BACKUP_DIR/risk_analysis_backup_$timestamp.duckdb"

    print_info "开始备份数据库..."
    cp "$DB_PATH" "$backup_file"

    if [ $? -eq 0 ]; then
        print_success "数据库备份完成: $backup_file"

        # 压缩备份文件
        gzip "$backup_file"
        print_success "备份文件已压缩: $backup_file.gz"

        # 显示备份文件大小
        local size=$(du -h "$backup_file.gz" | cut -f1)
        print_info "备份文件大小: $size"
    else
        print_error "数据库备份失败"
        exit 1
    fi
}

# 恢复数据库
restore_database() {
    print_info "可用的备份文件："
    ls -la "$BACKUP_DIR"/*.gz 2>/dev/null | nl

    if [ $? -ne 0 ]; then
        print_warning "没有找到备份文件"
        return 1
    fi

    echo
    read -p "请输入要恢复的备份文件编号: " backup_num

    local backup_files=($BACKUP_DIR/*.gz)
    local selected_backup="${backup_files[$((backup_num-1))]}"

    if [ ! -f "$selected_backup" ]; then
        print_error "无效的备份文件编号"
        return 1
    fi

    print_warning "恢复操作将覆盖当前数据库文件"
    read -p "确认继续? (y/n): " -n 1 -r
    echo

    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "开始恢复数据库..."

        # 备份当前数据库
        if [ -f "$DB_PATH" ]; then
            local current_backup="$BACKUP_DIR/current_backup_$(date +%Y%m%d_%H%M%S).duckdb"
            cp "$DB_PATH" "$current_backup"
            print_info "当前数据库已备份到: $current_backup"
        fi

        # 解压并恢复
        gunzip -c "$selected_backup" > "$DB_PATH"

        if [ $? -eq 0 ]; then
            print_success "数据库恢复完成"
        else
            print_error "数据库恢复失败"
        fi
    else
        print_info "恢复操作已取消"
    fi
}

# 查看数据库信息
show_database_info() {
    check_db_exists

    print_info "数据库信息："
    echo "文件路径: $DB_PATH"
    echo "文件大小: $(du -h "$DB_PATH" | cut -f1)"
    echo "修改时间: $(stat -c %y "$DB_PATH" 2>/dev/null || stat -f %Sm "$DB_PATH")"
    echo

    print_info "表统计信息："
    python3 "$SCRIPT_DIR/manage_duckbd/db_operations.py" info "$DB_PATH"
}

# 执行SQL查询
execute_sql() {
    check_db_exists

    if [ -n "$1" ]; then
        # 命令行参数提供的SQL
        local sql="$1"
        print_info "执行SQL: $sql"
        python3 "$SCRIPT_DIR/manage_duckbd/db_operations.py" sql "$DB_PATH" --sql "$sql"
    else
        # 交互式SQL - 提供一个简单的交互界面
        print_info "进入交互式SQL模式 (输入 'quit' 或 'exit' 退出)"
        while true; do
            read -p "SQL> " sql_input
            if [[ "$sql_input" == "quit" ]] || [[ "$sql_input" == "exit" ]] || [[ "$sql_input" == ".quit" ]]; then
                break
            fi
            if [ -n "$sql_input" ]; then
                python3 "$SCRIPT_DIR/manage_duckbd/db_operations.py" sql "$DB_PATH" --sql "$sql_input"
            fi
        done
    fi
}

# 清理数据库
clean_database() {
    check_db_exists

    echo -e "${YELLOW}清理选项:${NC}"
    echo "1. 清空业务数据表（保留用户认证数据）"
    echo "2. 清空所有数据表（保留表结构）"
    echo "3. 删除并重建数据库"
    echo "4. 重建user_trading_profiles表(113字段)"
    echo "5. 删除并重建业务表（保留认证表）"
    echo "6. 清理旧的备份文件"
    echo "7. 返回主菜单"

    read -p "请选择操作 (1-7): " -n 1 -r
    echo

    case $REPLY in
        1)
            print_warning "这将清空业务数据表，但保留用户认证数据和表结构"
            print_info "保留的认证表: auth_users, auth_user_sessions, auth_user_activity_logs, auth_system_config"
            read -p "确认继续? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                clean_business_data_only
                print_success "业务数据清空完成，用户认证数据已保留"
            fi
            ;;
        2)
            print_warning "这将清空所有数据表的数据，但保留表结构"
            print_error "注意：这会删除用户认证数据，导致无法登录！"
            read -p "确认继续? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                python3 "$SCRIPT_DIR/manage_duckbd/db_operations.py" clear "$DB_PATH"
                print_success "数据表清空完成"
                print_warning "用户认证数据已被清空，请使用选项6恢复默认用户"
            fi
            ;;
        3)
            print_warning "这将删除整个数据库并重新创建"
            print_error "注意：这会删除所有数据包括用户认证数据！"
            read -p "确认继续? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                rm -f "$DB_PATH"
                create_database
                print_warning "数据库已重建，请使用选项6恢复默认用户"
            fi
            ;;
        4)
            print_warning "这将重建user_trading_profiles表为完整的113字段版本"
            print_error "注意：这会删除该表中的所有现有数据！"
            read -p "确认继续? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                create_complete_user_trading_profiles_table
                if [ $? -eq 0 ]; then
                    print_success "user_trading_profiles表(113字段)重建完成"
                else
                    print_error "表重建失败"
                fi
            fi
            ;;
        5)
            print_warning "这将删除除认证表外的所有业务表，然后重新创建表结构"
            print_info "保留的认证表: auth_users, auth_user_sessions, auth_user_activity_logs, auth_system_config"
            print_error "注意：这会删除所有业务数据，但保留用户认证数据！"
            read -p "确认继续? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                drop_and_recreate_business_tables
                if [ $? -eq 0 ]; then
                    print_success "业务表删除并重建完成，用户认证数据已保留"
                else
                    print_error "业务表重建失败"
                fi
            fi
            ;;
        6)
            print_info "清理7天前的备份文件..."
            find "$BACKUP_DIR" -name "*.gz" -mtime +7 -delete
            print_success "旧备份文件清理完成"
            ;;
        7)
            return
            ;;
        *)
            print_error "无效选择"
            ;;
    esac
}

# 清理业务数据（保留认证数据）
clean_business_data_only() {
    print_info "开始清理业务数据表..."

    python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 定义要保留的认证相关表
    auth_tables = {
        'auth_users',
        'auth_user_sessions',
        'auth_user_activity_logs',
        'auth_system_config'
    }

    # 获取所有表名和类型
    tables_result = db_manager.execute_sql(\"\"\"
        SELECT table_name as name, table_type
        FROM information_schema.tables
        WHERE table_schema = 'main'
    \"\"\")

    if tables_result:
        cleared_count = 0
        preserved_count = 0
        skipped_views = 0

        for table_row in tables_result:
            table_name = table_row['name']
            table_type = table_row.get('table_type', 'BASE TABLE')

            # 跳过认证相关表
            if table_name in auth_tables:
                print(f'🔐 保留认证表: {table_name}')
                preserved_count += 1
                continue

            # 跳过视图，只清空基础表
            if table_type == 'VIEW':
                print(f'👁️ 跳过视图: {table_name}')
                skipped_views += 1
                continue

            # 清空业务表
            try:
                db_manager.execute_sql(f'DELETE FROM {table_name}')

                # 标记表类型
                if table_name in ['algorithm_results', 'wash_trading_results', 'same_account_wash_trading', 'cross_account_wash_trading']:
                    marker = '🆕'
                elif table_name in ['user_trading_profiles']:
                    marker = '👤'
                elif table_name in ['contract_risk_analysis']:
                    marker = '🔄'
                else:
                    marker = '📊'

                print(f'{marker} 已清空业务表: {table_name}')
                cleared_count += 1
            except Exception as e:
                print(f'❌ 清空表 {table_name} 失败: {e}')

        print(f'\\n📊 清理统计:')
        print(f'  ✅ 清空业务表: {cleared_count} 个')
        print(f'  🔐 保留认证表: {preserved_count} 个')
        print(f'  👁️ 跳过视图: {skipped_views} 个')
        print(f'  📈 总表数量: {len(tables_result)} 个')
    else:
        print('❌ 没有找到表')

except Exception as e:
    print(f'❌ 清理失败: {e}')
    sys.exit(1)
"
}

# 删除并重建业务表（保留认证表）
drop_and_recreate_business_tables() {
    print_info "开始删除并重建业务表..."

    python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager
from database.algorithm_storage_manager import AlgorithmStorageManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 定义要保留的认证相关表
    auth_tables = {
        'auth_users',
        'auth_user_sessions',
        'auth_user_activity_logs',
        'auth_system_config'
    }

    # 获取所有表名和类型
    tables_result = db_manager.execute_sql(\"\"\"
        SELECT table_name as name, table_type
        FROM information_schema.tables
        WHERE table_schema = 'main'
    \"\"\")

    if tables_result:
        dropped_count = 0
        preserved_count = 0
        skipped_views = 0

        # 第一步：删除业务表（按外键依赖顺序，先删子表再删父表）
        print('🗑️ 第一步：删除业务表...')

        # 收集当前表信息
        present_tables = {row['name']: row.get('table_type', 'BASE TABLE') for row in tables_result}
        dropped_tables = set()

        # 已知依赖顺序：先细节子表 → 聚合子表 → 主表
        preferred_drop_order = [
            'same_account_wash_trading',
            'cross_account_wash_trading',
            'wash_trading_pairs',
            'high_frequency_trading_details',
            'funding_rate_arbitrage_details',
            'wash_trading_results',
            'contract_risk_details',
            'algorithm_results'
        ]

        def safe_drop(table_name: str):
            global dropped_count, preserved_count, skipped_views
            if table_name in auth_tables:
                print(f'🔐 保留认证表: {table_name}')
                preserved_count += 1
                return
            table_type = present_tables.get(table_name, 'BASE TABLE')
            if table_type == 'VIEW':
                print(f'👁️ 跳过视图: {table_name}')
                skipped_views += 1
                return
            try:
                db_manager.execute_sql_no_return(f'DROP TABLE IF EXISTS {table_name}')
                print(f'🗑️ 已删除业务表: {table_name}')
                dropped_tables.add(table_name)
                dropped_count += 1
            except Exception as e:
                print(f'❌ 删除表 {table_name} 失败: {e}')

        # 1) 先按预定义顺序删除可能存在外键依赖的表
        for name in preferred_drop_order:
            if name in present_tables:
                safe_drop(name)

        # 2) 再删除其它剩余的基础表（排除认证表/视图/已删除表）
        for row in tables_result:
            name = row['name']
            if name in dropped_tables or name in auth_tables:
                continue
            if row.get('table_type', 'BASE TABLE') == 'VIEW':
                print(f'👁️ 跳过视图: {name}')
                skipped_views += 1
                continue
            safe_drop(name)

        print(f'\\n🔨 第二步：重新创建表结构...')

        # 第二步：使用DuckDBManager的initialize_database方法重新创建所有表
        try:
            db_manager.initialize_database()
            print('✅ 基础业务表结构重建完成')
            created_count = 6  # 基础表数量
        except Exception as e:
            print(f'❌ 重建基础表结构失败: {e}')
            created_count = 0

        # 第三步：重新创建算法结果表
        print('🔬 第三步：重新创建算法结果表...')
        try:
            storage_manager = AlgorithmStorageManager(db_manager)
            if storage_manager.initialize_tables():
                print('✅ 算法结果表创建成功')
            else:
                print('❌ 算法结果表创建失败')
        except Exception as e:
            print(f'❌ 算法结果表创建失败: {e}')

        print(f'\\n📊 重建统计:')
        print(f'  🗑️ 删除业务表: {dropped_count} 个')
        print(f'  🔐 保留认证表: {preserved_count} 个')
        print(f'  👁️ 跳过视图: {skipped_views} 个')
        print(f'  🔨 重建基础表: {created_count} 个')
        print(f'  📈 原表总数: {len(tables_result)} 个')
    else:
        print('❌ 没有找到表')

except Exception as e:
    print(f'❌ 重建失败: {e}')
    sys.exit(1)
"
}

# 导出数据
export_data() {
    check_db_exists

    local export_dir="./data/exports"
    mkdir -p "$export_dir"

    echo -e "${YELLOW}导出选项:${NC}"
    echo "1. 导出所有表为CSV"
    echo "2. 导出指定表为CSV"
    echo "3. 导出为SQL脚本"
    echo "4. 返回主菜单"

    read -p "请选择操作 (1-4): " -n 1 -r
    echo

    case $REPLY in
        1)
            print_info "导出所有表为CSV..."
            local timestamp=$(date +"%Y%m%d_%H%M%S")
            local export_subdir="$export_dir/full_export_$timestamp"
            mkdir -p "$export_subdir"

            # 使用Python导出所有表
            python3 -c "
import sys
import csv
import os
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 获取所有表名
    tables_result = db_manager.execute_sql('SHOW TABLES')
    if tables_result:
        for table_row in tables_result:
            table_name = table_row['name']
            print(f'导出表: {table_name}')

            # 获取表数据
            data = db_manager.execute_sql(f'SELECT * FROM {table_name}')

            # 导出为CSV
            csv_file = f'$export_subdir/{table_name}.csv'
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                if data:
                    writer = csv.DictWriter(f, fieldnames=data[0].keys())
                    writer.writeheader()
                    writer.writerows(data)
                    print(f'表 {table_name} 已导出 {len(data)} 行记录')
                else:
                    # 创建空文件
                    f.write('')
                    print(f'表 {table_name} 为空')
        print('所有表导出完成')
    else:
        print('没有找到表')
except Exception as e:
    print(f'导出失败: {e}')
"
            print_success "所有表已导出到: $export_subdir"
            ;;
        2)
            show_tables
            read -p "请输入要导出的表名: " table_name
            if [ -n "$table_name" ]; then
                local timestamp=$(date +"%Y%m%d_%H%M%S")
                local export_file="$export_dir/${table_name}_$timestamp.csv"
                python3 -c "
import sys
import csv
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')
    data = db_manager.execute_sql('SELECT * FROM $table_name')

    with open('$export_file', 'w', newline='', encoding='utf-8') as f:
        if data:
            writer = csv.DictWriter(f, fieldnames=data[0].keys())
            writer.writeheader()
            writer.writerows(data)
            print(f'表 $table_name 已导出 {len(data)} 行记录')
        else:
            f.write('')
            print(f'表 $table_name 为空')
except Exception as e:
    print(f'导出失败: {e}')
"
                print_success "表 $table_name 已导出到: $export_file"
            fi
            ;;
        3)
            local timestamp=$(date +"%Y%m%d_%H%M%S")
            local export_file="$export_dir/database_dump_$timestamp.sql"
            python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    with open('$export_file', 'w', encoding='utf-8') as f:
        # 导出表结构和数据
        tables_result = db_manager.execute_sql('SHOW TABLES')
        if tables_result:
            for table_row in tables_result:
                table_name = table_row['name']

                # 获取建表语句 (使用DESCRIBE来重构)
                try:
                    describe_result = db_manager.execute_sql(f'DESCRIBE {table_name}')
                    if describe_result:
                        columns = []
                        for col in describe_result:
                            col_def = f\"{col['column_name']} {col['column_type']}\"
                            if col.get('null') == 'NO':
                                col_def += ' NOT NULL'
                            columns.append(col_def)
                        create_sql = f'CREATE TABLE {table_name} (\\n  ' + ',\\n  '.join(columns) + '\\n);'
                                                 f.write(f'{create_sql}\\n\\n')
                 except:
                     f.write(f'-- 无法获取表 {table_name} 的结构\\n\\n')

                # 获取数据并生成INSERT语句
                data = db_manager.execute_sql(f'SELECT * FROM {table_name}')
                if data:
                    for row in data:
                        columns = ', '.join(row.keys())
                        values = ', '.join([f\"'{str(v).replace(\"'\", \"''\")}'\" if v is not None else 'NULL' for v in row.values()])
                        f.write(f'INSERT INTO {table_name} ({columns}) VALUES ({values});\\n')
                    f.write('\\n')

        print(f'数据库结构和数据已导出到: $export_file')
except Exception as e:
    print(f'导出失败: {e}')
"
            print_success "数据库已导出为SQL脚本: $export_file"
            ;;
        4)
            return
            ;;
        *)
            print_error "无效选择"
            ;;
    esac
}

# 显示所有表
show_tables() {
    check_db_exists
    print_info "数据库中的表："
    python3 "$SCRIPT_DIR/manage_duckbd/db_operations.py" tables "$DB_PATH"
}

# 显示表结构
describe_table() {
    check_db_exists
    show_tables
    read -p "请输入要查看的表名: " table_name
    if [ -n "$table_name" ]; then
        print_info "表 $table_name 的结构："
        python3 "$SCRIPT_DIR/manage_duckbd/db_operations.py" describe "$DB_PATH" --table "$table_name"
    fi
}

# 性能分析
analyze_performance() {
    check_db_exists

    print_info "数据库性能分析："

    # 数据库大小
    echo "数据库文件大小: $(du -h "$DB_PATH" | cut -f1)"

    # 表大小统计
    print_info "各表记录数统计："
    python3 "$SCRIPT_DIR/manage_duckbd/db_operations.py" stats "$DB_PATH"

    # 索引信息
    print_info "索引信息："
    python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 获取所有表名
    tables_result = db_manager.execute_sql('SHOW TABLES')
    if tables_result:
        print('表名\t\t\t索引信息')
        print('-' * 50)
        for table_row in tables_result:
            table_name = table_row['name']
            # 获取表的索引信息
            try:
                indexes = db_manager.execute_sql(f'PRAGMA table_info({table_name})')
                if indexes:
                    index_info = []
                    for col in indexes:
                        if col.get('pk', 0) > 0:
                            index_info.append(f\"{col['name']}(PK)\")
                    if index_info:
                        index_str = ', '.join(index_info)
                        print(f'{table_name:<20}\t{index_str}')
                    else:
                        print(f'{table_name:<20}\t无索引')
            except:
                print(f'{table_name:<20}\t无法获取索引信息')
    else:
        print('没有找到表')

except Exception as e:
    print(f'获取索引信息失败: {e}')
"
}

# 主菜单
show_menu() {
    clear
    print_header
    echo
    echo -e "${CYAN}请选择操作:${NC}"
    echo "1.  查看数据库信息"
    echo "2.  备份数据库"
    echo "3.  恢复数据库"
    echo "4.  执行SQL查询"
    echo "5.  显示所有表"
    echo "6.  用户认证管理"
    echo "7.  查看表结构"
    echo "8.  清理数据库"
    echo "9.  导出数据"
    echo "10. 性能分析"
    echo "11. 创建新数据库"
    echo "12. 存储结构管理"
    echo "13. 数据库验证和清理"
    echo "14. 查看操作日志"
    echo "0.  退出"
    echo
}


# 更新已有表结构，确保新增字段存在（trade_sequence 等）
update_existing_tables_schema() {
    check_db_exists
    print_info "开始更新表结构..."

    python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

DB_PATH = '$DB_PATH'

try:
    db = DuckDBManager(DB_PATH)

    def table_exists(table_name: str) -> bool:
        try:
            res = db.execute_sql(\"SELECT table_name FROM information_schema.tables WHERE table_name = ?\", [table_name])
            if res:
                return True
        except Exception:
            pass
        try:
            tables = db.execute_sql('SHOW TABLES') or []
            names = []
            for t in tables:
                try:
                    names.append(t['name'])
                except Exception:
                    names.append(t[0])
            return table_name in names
        except Exception:
            return False

    def get_columns(table: str):
        try:
            cols = db.execute_sql(f'DESCRIBE {table}') or []
            names = []
            for col in cols:
                try:
                    names.append(col['column_name'])
                except Exception:
                    names.append(col[0])
            return names
        except Exception:
            return []

    def ensure_column(table: str, column: str, coltype: str) -> bool:
        if not table_exists(table):
            print(f'⚠️  表不存在，跳过: {table}')
            return True  # 不把不存在表当失败
        try:
            names = get_columns(table)
            if column not in names:
                db.execute_sql(f'ALTER TABLE {table} ADD COLUMN {column} {coltype}')
                print(f'✅ 已添加字段: {table}.{column}')
            else:
                print(f'ℹ️ 字段已存在: {table}.{column}，跳过')
            return True
        except Exception as e:
            print(f'❌ 更新失败: {table}.{column}: {e}')
            return False

    ok = True
    # 新增字段：交易序列
    ok = ensure_column('position_analysis', 'trade_sequence', 'TEXT DEFAULT NULL') and ok
    ok = ensure_column('incomplete_positions_waiting', 'trade_sequence', 'TEXT DEFAULT NULL') and ok

    # 安全兜底：仓位模式统计字段（若历史环境缺失则补齐）
    ok = ensure_column('position_analysis', 'cross_margin_positions', 'INTEGER DEFAULT 0') and ok
    ok = ensure_column('position_analysis', 'isolated_margin_positions', 'INTEGER DEFAULT 0') and ok

    if not ok:
        sys.exit(1)
    print('✅ 表结构更新流程完成')
except Exception as e:
    print(f'❌ 执行更新流程失败: {e}')
    import traceback; traceback.print_exc()
    sys.exit(1)
"
}

# 存储结构管理
manage_storage_structure() {
    check_db_exists

    echo -e "${YELLOW}存储结构管理:${NC}"
    echo "1. 检查存储结构状态"
    echo "2. 初始化新存储结构"
    echo "3. 初始化用户行为分析表"
    echo "4. 验证113字段完整性"
    echo "5. 重建完整的113字段表结构"
    echo "6. 数据迁移到新存储"
    echo "7. 清理空表"
    echo "8. 更新表结构（添加trade_sequence字段）"  # 🚀 新增选项
    echo "9. 返回主菜单"

    read -p "请选择操作 (1-9): " -n 1 -r
    echo

    case $REPLY in
        1)
            print_info "检查存储结构状态..."
            python3 "$SCRIPT_DIR/manage_duckbd/db_operations.py" check_storage "$DB_PATH"
            ;;
        2)
            print_info "初始化新存储结构..."
            python3 "$SCRIPT_DIR/manage_duckbd/db_operations.py" init_storage "$DB_PATH"
            if [ $? -eq 0 ]; then
                print_success "新存储结构初始化成功"
            else
                print_error "新存储结构初始化失败"
            fi
            ;;
        3)
            print_info "初始化用户行为分析表..."
            python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db = DuckDBManager('$DB_PATH')

    # 检查表是否已存在
    tables = db.execute_sql('SHOW TABLES')
    table_names = [t['name'] for t in tables]

    if 'user_trading_profiles' in table_names:
        print('✅ user_trading_profiles表已存在')
        result = db.execute_sql('SELECT COUNT(*) as count FROM user_trading_profiles')
        count = result[0]['count'] if result else 0
        print(f'  📊 当前记录数: {count}')

        schema = db.execute_sql('DESCRIBE user_trading_profiles')
        field_count = len(schema)
        print(f'  🔢 字段数量: {field_count}')

        # 验证是否为完整的113字段表
        if field_count >= 113:
            print(f'  🎉 字段完整性: 完整 ({field_count}/113)')

            # 检查关键字段
            field_names = [f[\"column_name\"] for f in schema]
            key_fields = ['professional_score', 'trader_type', 'execution_efficiency_score', 'max_single_loss_score']
            missing_fields = [f for f in key_fields if f not in field_names]

            if not missing_fields:
                print(f'  ✅ 关键字段: 完整')
            else:
                print(f'  ⚠️  缺失关键字段: {missing_fields}')

        elif field_count >= 110:
            print(f'  ⚠️  字段完整性: 基本完整 ({field_count}/113)')
        elif field_count >= 100:
            print(f'  ⚠️  字段完整性: 较完整 ({field_count}/113)')
        else:
            print(f'  ❌ 字段完整性: 不完整 ({field_count}/113)')
            print(f'     建议使用选项5重建完整表结构')

    else:
        print('❌ user_trading_profiles表不存在，请使用选项11重新创建数据库')

except Exception as e:
    print(f'检查失败: {e}')
"
            ;;
        4)
            print_info "验证113字段完整性..."
            python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db = DuckDBManager('$DB_PATH')

    # 检查表是否存在
    tables = db.execute_sql('SHOW TABLES')
    table_names = [t['name'] for t in tables]

    if 'user_trading_profiles' not in table_names:
        print('❌ user_trading_profiles表不存在')
        print('   请使用选项2初始化新存储结构')
        sys.exit(1)

    # 获取表结构
    schema = db.execute_sql('DESCRIBE user_trading_profiles')
    field_count = len(schema)

    print(f'🔍 user_trading_profiles表字段验证:')
    print(f'  📊 实际字段数量: {field_count}')
    print(f'  🎯 期望字段数量: 113')
    print(f'  📈 完整性: {field_count/113*100:.1f}%')

    if field_count == 113:
        print(f'  ✅ 字段完整性: 完美 ✨')

        # 验证关键字段
        field_names = [f[\"column_name\"] for f in schema]

        # 检查各类字段
        categories = {
            '基础字段': ['id', 'member_id', 'analysis_date'],
            '专业度评分': ['professional_score', 'profitability_score', 'risk_control_score'],
            '详细评分': ['execution_efficiency_score', 'timing_ability_score', 'risk_discipline_score'],
            '用户分类': ['trader_type', 'confidence_level'],
            '个人分析专用': ['last_activity_time', 'max_single_loss_score'],
            '异常交易分析': ['wash_trading_volume', 'high_frequency_volume', 'funding_arbitrage_volume']
        }

        all_present = True
        for category, fields in categories.items():
            missing = [f for f in fields if f not in field_names]
            if missing:
                print(f'  ❌ {category}: 缺失 {missing}')
                all_present = False
            else:
                print(f'  ✅ {category}: 完整')

        if all_present:
            print(f'\\n🎉 恭喜！user_trading_profiles表包含完整的113个字段！')
            print(f'   所有关键字段都已正确配置，可以支持完整的用户画像分析。')
        else:
            print(f'\\n⚠️  表结构基本完整，但部分关键字段缺失')

    elif field_count > 113:
        print(f'  ⚠️  字段数量超出预期 (+{field_count-113}个)')
        print(f'     可能包含了额外的字段')

    elif field_count >= 110:
        print(f'  ⚠️  字段基本完整，缺少 {113-field_count} 个字段')
        print(f'     建议重建表以获得完整的113字段支持')

    elif field_count >= 100:
        print(f'  ⚠️  字段较完整，缺少 {113-field_count} 个字段')
        print(f'     建议重建表以获得完整的113字段支持')

    else:
        print(f'  ❌ 字段严重不足，缺少 {113-field_count} 个字段')
        print(f'     必须重建表才能正常使用')

    # 提供修复建议
    if field_count != 113:
        print(f'\\n🔧 修复建议:')
        print(f'   1. 使用选项5重建完整的113字段表结构')
        print(f'   2. 或使用选项11重新创建数据库')

except Exception as e:
                print(f'验证失败: {e}')
    import traceback
    traceback.print_exc()
"
            ;;
        5)
            print_info "重建完整的113字段表结构..."
            print_warning "这将删除现有的user_trading_profiles表并重新创建包含完整113字段的新表"
            print_error "注意：这会删除该表中所有现有数据！"
            read -p "确认继续? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                # 先备份现有数据(如果表存在)
                python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager
import json
import os

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 检查表是否存在
    tables = db_manager.execute_sql('SHOW TABLES')
    table_names = [t['name'] for t in tables] if tables else []

    if 'user_trading_profiles' in table_names:
        print('🔄 备份现有数据...')
        # 获取现有数据
        existing_data = db_manager.execute_sql('SELECT * FROM user_trading_profiles')

        if existing_data:
            # 保存到备份文件
            backup_file = './temp/user_trading_profiles_backup_before_rebuild.json'
            os.makedirs(os.path.dirname(backup_file), exist_ok=True)
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, indent=2, default=str)
            print(f'✅ 数据已备份到: {backup_file}')
            print(f'   备份记录数: {len(existing_data)}')

        # 删除现有表
        db_manager.execute_sql('DROP TABLE IF EXISTS user_trading_profiles')
        print('🗑️  现有表已删除')
    else:
        print('ℹ️  表不存在，将直接创建新表')

except Exception as e:
    print(f'备份过程出错: {e}')
    # 继续执行，不阻止表重建
"

                # 创建完整的113字段表
                create_complete_user_trading_profiles_table

                if [ $? -eq 0 ]; then
                    print_success "完整的113字段表重建成功！"
                    print_info "如果有备份数据，请手动决定是否需要数据迁移"
                else
                    print_error "表重建失败"
                fi
            else
                print_info "操作已取消"
            fi
            ;;
        6)
            print_info "数据迁移到新存储..."
            print_warning "这将把旧存储中的数据迁移到新存储结构"
            read -p "确认继续? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager
from database.algorithm_storage_manager import AlgorithmStorageManager

try:
    db_manager = DuckDBManager('$DB_PATH')
    storage_manager = AlgorithmStorageManager(db_manager)

    print('🔄 数据迁移状态检查:')
    print('  ✅ 新存储结构已完成重构')
    print('  ✅ 数据通过repository层自动适配')
    print('  ✅ API接口保持向后兼容')
    print('\\n📊 当前存储架构:')
    print('  🆕 algorithm_results - 算法结果主表')
    print('  🆕 wash_trading_results - 对敲交易统计')
    print('  🆕 same_account_wash_trading - 同账户对敲详情')
    print('  🆕 cross_account_wash_trading - 跨账户对敲详情')
    print('  🆕 user_trading_profiles - 用户交易画像(113字段)')
    print('  🔄 contract_risk_analysis - 兼容性适配器')

except Exception as e:
    print(f'检查失败: {e}')
"
            fi
            ;;
        7)
            print_info "清理空表..."
            print_warning "这将删除没有数据的表"
            read -p "确认继续? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                python3 "$SCRIPT_DIR/manage_duckbd/db_operations.py" clean_empty "$DB_PATH"
                print_success "空表清理完成"
            fi
            ;;
        8)
            print_info "更新表结构，添加trade_sequence字段..."
            update_existing_tables_schema
            if [ $? -eq 0 ]; then
                print_success "表结构更新成功"
            else
                print_error "表结构更新失败"
            fi
            ;;
        9)
            return
            ;;
        *)
            print_error "无效选择"
            ;;
    esac
}

# 用户认证管理
manage_user_auth() {
    check_db_exists

    echo -e "${YELLOW}用户认证管理:${NC}"
    echo "1. 查看现有用户"
    echo "2. 创建默认管理员用户"
    echo "3. 重置用户密码"
    echo "4. 删除用户"
    echo "5. 清理过期会话"
    echo "6. 恢复完整认证结构"
    echo "7. 返回主菜单"

    read -p "请选择操作 (1-7): " -n 1 -r
    echo

    case $REPLY in
        1)
            show_auth_users
            ;;
        2)
            create_default_admin
            ;;
        3)
            reset_user_password
            ;;
        4)
            delete_user
            ;;
        5)
            clean_expired_sessions
            ;;
        6)
            restore_auth_structure
            ;;
        7)
            return
            ;;
        *)
            print_error "无效选择"
            ;;
    esac
}

# 查看现有用户
show_auth_users() {
    print_info "现有用户列表："
    python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 检查auth_users表是否存在
    tables = db_manager.execute_sql('SHOW TABLES')
    table_names = [t['name'] for t in tables] if tables else []

    if 'auth_users' not in table_names:
        print('认证表不存在，请先恢复认证结构')
        sys.exit(1)

    users = db_manager.execute_sql('SELECT id, username, role, email, created_at, last_login, is_active FROM auth_users ORDER BY id')

    if users:
        print('ID\t用户名\t\t角色\t\t邮箱\t\t\t创建时间\t\t最后登录\t\t状态')
        print('-' * 100)
        for user in users:
            status = '激活' if user['is_active'] else '禁用'
            last_login = user['last_login'] or '从未登录'
            print(f'{user[\"id\"]}\t{user[\"username\"]:<15}\t{user[\"role\"]:<10}\t{user[\"email\"] or \"未设置\":<20}\t{user[\"created_at\"]}\t{last_login}\t{status}')
    else:
        print('没有找到用户')

except Exception as e:
    print(f'查看用户失败: {e}')
"
}

# 创建默认管理员用户
create_default_admin() {
    print_info "创建默认管理员用户..."
    print_warning "默认用户名: admin, 默认密码: admin123"

    read -p "确认创建? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        python3 -c "
import sys
import bcrypt
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 确保认证表存在
    tables = db_manager.execute_sql('SHOW TABLES')
    table_names = [t['name'] for t in tables] if tables else []

    if 'auth_users' not in table_names:
        print('认证表不存在，正在创建...')
        # 执行认证表创建脚本
        with open('./backend/database/schema/auth_tables.sql', 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        db_manager.execute_script(schema_sql)
        print('认证表创建完成')

    # 检查是否已存在admin用户
    existing = db_manager.execute_sql('SELECT id FROM auth_users WHERE username = ?', ['admin'])
    if existing:
        print('admin用户已存在')
        sys.exit(0)

    # 创建admin用户 - 使用bcrypt加密密码
    password = 'admin123'
    salt = bcrypt.gensalt(rounds=12)
    password_hash = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

    db_manager.execute_sql('''
        INSERT INTO auth_users (username, password_hash, role, email, is_active)
        VALUES (?, ?, ?, ?, ?)
    ''', ['admin', password_hash, 'admin', '<EMAIL>', True])

    print('默认管理员用户创建成功')
    print('用户名: admin')
    print('密码: admin123')
    print('角色: admin')
    print('')
    print('⚠️  请登录后立即修改密码！')

except Exception as e:
    print(f'创建用户失败: {e}')
    import traceback
    traceback.print_exc()
"
        if [ $? -eq 0 ]; then
            print_success "默认管理员用户创建成功"
        else
            print_error "默认管理员用户创建失败"
        fi
    fi
}

# 重置用户密码
reset_user_password() {
    show_auth_users
    echo
    read -p "请输入要重置密码的用户名: " username
    if [ -n "$username" ]; then
        read -p "请输入新密码: " new_password
        if [ -n "$new_password" ]; then
            python3 -c "
import sys
import bcrypt
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 检查用户是否存在
    user = db_manager.execute_sql('SELECT id FROM auth_users WHERE username = ?', ['$username'])
    if not user:
        print(f'用户 $username 不存在')
        sys.exit(1)

    # 重置密码 - 使用bcrypt加密
    password = '$new_password'
    salt = bcrypt.gensalt(rounds=12)
    password_hash = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

    db_manager.execute_sql('''
        UPDATE auth_users
        SET password_hash = ?, updated_at = CURRENT_TIMESTAMP, login_attempts = 0, locked_until = NULL
        WHERE username = ?
    ''', [password_hash, '$username'])

    print(f'用户 $username 的密码已重置')

except Exception as e:
    print(f'重置密码失败: {e}')
"
        fi
    fi
}

# 删除用户
delete_user() {
    show_auth_users
    echo
    read -p "请输入要删除的用户名: " username
    if [ -n "$username" ]; then
        if [ "$username" = "admin" ]; then
            print_warning "警告：删除admin用户可能导致无法管理系统"
        fi

        read -p "确认删除用户 $username? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 删除用户相关数据
    db_manager.execute_sql('DELETE FROM auth_user_sessions WHERE user_id = (SELECT id FROM auth_users WHERE username = ?)', ['$username'])
    db_manager.execute_sql('DELETE FROM auth_user_activity_logs WHERE user_id = (SELECT id FROM auth_users WHERE username = ?)', ['$username'])
    result = db_manager.execute_sql('DELETE FROM auth_users WHERE username = ?', ['$username'])

    print(f'用户 $username 及相关数据已删除')

except Exception as e:
    print(f'删除用户失败: {e}')
"
        fi
    fi
}

# 清理过期会话
clean_expired_sessions() {
    print_info "清理过期会话..."
    python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 删除过期会话
    result = db_manager.execute_sql('DELETE FROM auth_user_sessions WHERE expires_at < CURRENT_TIMESTAMP')

    # 统计剩余会话
    remaining = db_manager.execute_sql('SELECT COUNT(*) as count FROM auth_user_sessions')
    count = remaining[0]['count'] if remaining else 0

    print(f'过期会话已清理')
    print(f'剩余活跃会话: {count}')

except Exception as e:
    print(f'清理会话失败: {e}')
"
}

# 恢复认证结构
restore_auth_structure() {
    print_info "恢复完整认证结构..."
    print_warning "这将重新创建所有认证相关表和默认配置"

    read -p "确认继续? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        python3 -c "
import sys
sys.path.append('./backend')
from database.duckdb_manager import DuckDBManager

try:
    db_manager = DuckDBManager('$DB_PATH')

    # 执行认证表创建脚本
    with open('./backend/database/schema/auth_tables.sql', 'r', encoding='utf-8') as f:
        schema_sql = f.read()

    db_manager.execute_script(schema_sql)
    print('认证结构恢复完成')

    # 检查是否需要创建默认用户
    users = db_manager.execute_sql('SELECT COUNT(*) as count FROM auth_users')
    user_count = users[0]['count'] if users else 0

    if user_count == 0:
        print('\\n检测到没有用户，建议创建默认管理员用户')
        print('请返回选择选项2创建默认用户')
    else:
        print(f'\\n当前有 {user_count} 个用户')

except Exception as e:
    print(f'恢复认证结构失败: {e}')
    import traceback
    traceback.print_exc()
"
        if [ $? -eq 0 ]; then
            print_success "认证结构恢复完成"
        else
            print_error "认证结构恢复失败"
        fi
    fi
}

# 查看操作日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        print_info "最近的操作日志："
        tail -20 "$LOG_FILE"
    else
        print_warning "没有找到日志文件"
    fi
}

# 主程序
main() {
    # 检查DuckDB是否安装
    check_duckdb

    # 如果有命令行参数，直接执行
    if [ $# -gt 0 ]; then
        case $1 in
            "backup")
                backup_database
                ;;
            "info")
                show_database_info
                ;;
            "tables")
                show_tables
                ;;
            "sql")
                execute_sql "$2"
                ;;
            "clean")
                clean_database
                ;;
            "export")
                export_data
                ;;
            "analyze")
                analyze_performance
                ;;
            "storage")
                manage_storage_structure
                ;;
            "auth")
                manage_user_auth
                ;;
            "restore-admin")
                create_default_admin
                ;;
            *)
                echo "用法: $0 [backup|info|tables|sql|clean|export|analyze|storage|auth|restore-admin]"
                echo "或直接运行 $0 进入交互模式"
                echo ""
                echo "特殊命令："
                echo "  restore-admin  - 快速创建默认管理员用户(admin/admin123)"
                ;;
        esac
        exit 0
    fi

    # 交互模式
    while true; do
        show_menu
        read -p "请输入选择 (0-14): " -n 2 -r
        echo

        case $REPLY in
            1)
                show_database_info
                ;;
            2)
                backup_database
                ;;
            3)
                restore_database
                ;;
            4)
                execute_sql
                ;;
            5)
                show_tables
                ;;
            6)
                manage_user_auth
                ;;
            7)
                describe_table
                ;;
            8)
                clean_database
                ;;
            9)
                export_data
                ;;
            10)
                analyze_performance
                ;;
            11)
                create_database
                ;;
            12)
                manage_storage_structure
                ;;
            13)
                manage_database_validation
                ;;
            14)
                show_logs
                ;;
            0)
                print_success "再见！"
                exit 0
                ;;
            *)
                print_error "无效选择，请重新输入"
                ;;
        esac

        echo
        read -p "按回车键继续..." -r
    done
}

# 运行主程序
main "$@"
